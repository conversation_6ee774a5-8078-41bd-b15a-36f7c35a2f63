{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myAddress/myAddress.vue?abd7", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myAddress/myAddress.vue?6b50", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myAddress/myAddress.vue?0e02", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myAddress/myAddress.vue?6f44", "uni-app:///pages/mine/myAddress/myAddress.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myAddress/myAddress.vue?73d3", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myAddress/myAddress.vue?1164"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "popuoShow", "isChoose", "userId", "addressList", "<PERSON><PERSON><PERSON><PERSON>", "addressParams", "name", "phone", "address", "rules", "type", "required", "message", "trigger", "onLoad", "console", "onReady", "methods", "getAddressList", "pageSize", "closeBtn", "confirmBtn", "clearParams", "showPopup", "payChooseAddress", "confirmChoose", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC4K;AAC5K,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAmpB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACgDvqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAN;MACA;MACAO;QACA;UACAC;UACAC;UACAC;UACAC;QACA;QACA;UACAH;UACAC;UACAC;UACAC;QACA;QACA;UACAH;UACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACAC;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;QACAjB;MACA;QACA;MACA;IACA;IACAkB;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;UACA;QACA;MACA,4BAEA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;QACAT;MACA;IACA;IACAU;MACA;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjJA;AAAA;AAAA;AAAA;AAAkuC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAtvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/myAddress/myAddress.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/myAddress/myAddress.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myAddress.vue?vue&type=template&id=6c62653e&\"\nvar renderjs\nimport script from \"./myAddress.vue?vue&type=script&lang=js&\"\nexport * from \"./myAddress.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myAddress.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/myAddress/myAddress.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myAddress.vue?vue&type=template&id=6c62653e&\"", "var components\ntry {\n  components = {\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    \"u-Form\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--form/u--form\" */ \"@/uni_modules/uview-ui/components/u--form/u--form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--input/u--input\" */ \"@/uni_modules/uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    \"u-Textarea\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--textarea/u--textarea\" */ \"@/uni_modules/uview-ui/components/u--textarea/u--textarea.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myAddress.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myAddress.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"address_page\">\r\n\t\t<view class=\"address_list\">\r\n\t\t\t<view v-for=\"item in addressList\" :key=\"item.id\" @click=\"payChooseAddress(item)\" :class=\"chooseAddress.id===item.id?'choosed_item':'address_item'\" >\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t收件人：{{item.name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t联系电话：{{item.phone}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t地址：{{item.address}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"add_btn\">\r\n\t\t\t<u-button @click=\"showPopup\" text=\"新增地址\" type=\"warning\"></u-button>\r\n\t\t\t<u-button v-if=\"isChoose==1\" @click=\"confirmChoose\" text=\"确认选择\" type=\"primary\"></u-button>\r\n\t\t</view>\r\n\t\t<u-popup :show=\"popuoShow\" mode=\"center\" round=\"12\" @onclose=\"closeBtn\">\r\n\t\t\t<view style=\"padding: 20rpx;width: 80vw;\">\r\n\t\t\t\t<u--form labelPosition=\"left\" :model=\"addressParams\" :rules=\"rules\" ref=\"uForm\" labelWidth=\"60\">\r\n\t\t\t\t\t<u-form-item label=\"收件人:\" prop=\"name\" borderBottom ref=\"item1\">\r\n\t\t\t\t\t\t<u--input v-model=\"addressParams.name\" placeholder=\"请输入姓名\"></u--input>\r\n\t\t\t\t\t</u-form-item>\r\n\t\t\t\t\t<u-form-item label=\"联系电话:\" prop=\"phone\" borderBottom ref=\"item2\">\r\n\t\t\t\t\t\t<u--input v-model=\"addressParams.phone\" placeholder=\"请输入联系电话\"></u--input>\r\n\t\t\t\t\t</u-form-item>\r\n\t\t\t\t\t<u-form-item label=\"收货地址:\" prop=\"address\" borderBottom ref=\"item3\">\r\n\t\t\t\t\t\t<u--textarea v-model=\"addressParams.address\" placeholder=\"请输入内容\" ></u--textarea>\r\n\t\t\t\t\t</u-form-item>\r\n\t\t\t\t</u--form>\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;padding: 0 120rpx;margin-top: 30rpx;\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<u-button type=\"info\" text=\"取消\" @click=\"closeBtn\"></u-button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<u-button type=\"primary\" text=\"确定\" @click=\"confirmBtn\"></u-button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tlistAddress,\r\n\t\taddAddress,\r\n\t\tdelAddress\r\n\t} from \"@/api/pcb/address.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpopuoShow:false,\r\n\t\t\t\tisChoose:0,\r\n\t\t\t\tuserId: '',\r\n\t\t\t\taddressList: [],\r\n\t\t\t\tchooseAddress:null,\r\n\t\t\t\taddressParams:{\r\n\t\t\t\t\tname:'',\r\n\t\t\t\t\tphone:'',\r\n\t\t\t\t\taddress:'',\r\n\t\t\t\t\tuserId:''\r\n\t\t\t\t},\r\n\t\t\t\trules: {\r\n\t\t\t\t\t'name': {\r\n\t\t\t\t\t\ttype: 'string',\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请填写姓名',\r\n\t\t\t\t\t\ttrigger: ['blur', 'change']\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'phone': {\r\n\t\t\t\t\t\ttype: 'string',\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请填写联系电话',\r\n\t\t\t\t\t\ttrigger: ['blur', 'change']\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'address': {\r\n\t\t\t\t\t\ttype: 'string',\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请填写收货地址',\r\n\t\t\t\t\t\ttrigger: ['blur', 'change']\r\n\t\t\t\t\t},\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad({a}) {\r\n\t\t\tthis.userId = this.$store.state.user.userId\r\n\t\t\tthis.addressParams.userId = this.$store.state.user.userId\r\n\t\t\tthis.getAddressList()\r\n\t\t\tconsole.log(\"a\",a);\r\n\t\t\tif(a==1){\r\n\t\t\t\tthis.isChoose = a\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t\t//onReady 为uni-app支持的生命周期之一\r\n\t\t    \tthis.$refs.uForm.setRules(this.rules)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetAddressList() {\r\n\t\t\t\tlistAddress({\r\n\t\t\t\t\tpageSize: 100000,\r\n\t\t\t\t\tuserId: this.userId\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.addressList = res.rows\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseBtn(){\r\n\t\t\t\tthis.clearParams()\r\n\t\t\t\tthis.popuoShow = false\r\n\t\t\t},\r\n\t\t\tconfirmBtn(){\r\n\t\t\t\tthis.$refs.uForm.validate().then(res => {\r\n\t\t\t\t\taddAddress(this.addressParams).then(res=>{\r\n\t\t\t\t\t\tthis.getAddressList()\r\n\t\t\t\t\t\tthis.clearParams()\r\n\t\t\t\t\t\tthis.popuoShow = false\r\n\t\t\t\t\t})\r\n\t\t\t\t}).catch(errors => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclearParams(){\r\n\t\t\t\tthis.addressParams.name = ''\r\n\t\t\t\tthis.addressParams.phone = ''\r\n\t\t\t\tthis.addressParams.address = ''\r\n\t\t\t},\r\n\t\t\tshowPopup(){\r\n\t\t\t\tthis.popuoShow = true\r\n\t\t\t},\r\n\t\t\tpayChooseAddress(item){\r\n\t\t\t\tif(this.isChoose==1){\r\n\t\t\t\t\tthis.chooseAddress = item\r\n\t\t\t\t\tconsole.log(this.chooseAddress);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tconfirmChoose(){\r\n\t\t\t\tthis.$noti.post('chooseAddress',this.chooseAddress)\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.address_page {\r\n\t\tpadding-top: 20rpx;\r\n\t\t.address_list {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tgap: 16rpx;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\t.address_item {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\twidth: 95vw;\r\n\t\t\t\tpadding: 18rpx;\r\n\t\t\t\tborder: 1rpx solid #fff;\r\n\t\t\t}\r\n\t\t\t.choosed_item{\r\n\t\t\t\tborder: 1rpx solid rgb(255,153,0);\r\n\t\t\t\tbackground-color: rgba(255,153,0,0.05);\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\twidth: 95vw;\r\n\t\t\t\tpadding: 18rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.add_btn{\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 30rpx;\r\n\t\t\twidth: 80vw;\r\n\t\t\t// height: 100rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tgap: 30rpx;\r\n\t\t\tleft: 10vw;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myAddress.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myAddress.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881747\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}