<template>
	<view class="address_page">
		<view class="address_list">
			<view v-for="item in addressList" :key="item.id" @click="payChooseAddress(item)" :class="chooseAddress.id===item.id?'choosed_item':'address_item'" >
				<view style="display: flex;justify-content: space-between;">
					<view class="">
						收件人：{{item.name}}
					</view>
					<view class="">
						联系电话：{{item.phone}}
					</view>
				</view>
				<view class="">
					地址：{{item.address}}
				</view>
			</view>
		</view>
		<view class="add_btn">
			<u-button @click="showPopup" text="新增地址" type="warning"></u-button>
			<u-button v-if="isChoose==1" @click="confirmChoose" text="确认选择" type="primary"></u-button>
		</view>
		<u-popup :show="popuoShow" mode="center" round="12" @onclose="closeBtn">
			<view style="padding: 20rpx;width: 80vw;">
				<u--form labelPosition="left" :model="addressParams" :rules="rules" ref="uForm" labelWidth="60">
					<u-form-item label="收件人:" prop="name" borderBottom ref="item1">
						<u--input v-model="addressParams.name" placeholder="请输入姓名"></u--input>
					</u-form-item>
					<u-form-item label="联系电话:" prop="phone" borderBottom ref="item2">
						<u--input v-model="addressParams.phone" placeholder="请输入联系电话"></u--input>
					</u-form-item>
					<u-form-item label="收货地址:" prop="address" borderBottom ref="item3">
						<u--textarea v-model="addressParams.address" placeholder="请输入内容" ></u--textarea>
					</u-form-item>
				</u--form>
				<view style="display: flex;justify-content: space-between;padding: 0 120rpx;margin-top: 30rpx;">
					<view class="">
						<u-button type="info" text="取消" @click="closeBtn"></u-button>
					</view>
					<view class="">
						<u-button type="primary" text="确定" @click="confirmBtn"></u-button>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		listAddress,
		addAddress,
		delAddress
	} from "@/api/pcb/address.js"
	export default {
		data() {
			return {
				popuoShow:false,
				isChoose:0,
				userId: '',
				addressList: [],
				chooseAddress:null,
				addressParams:{
					name:'',
					phone:'',
					address:'',
					userId:''
				},
				rules: {
					'name': {
						type: 'string',
						required: true,
						message: '请填写姓名',
						trigger: ['blur', 'change']
					},
					'phone': {
						type: 'string',
						required: true,
						message: '请填写联系电话',
						trigger: ['blur', 'change']
					},
					'address': {
						type: 'string',
						required: true,
						message: '请填写收货地址',
						trigger: ['blur', 'change']
					},
				},
			}
		},
		onLoad({a}) {
			this.userId = this.$store.state.user.userId
			this.addressParams.userId = this.$store.state.user.userId
			this.getAddressList()
			console.log("a",a);
			if(a==1){
				this.isChoose = a
			}
		},
		onReady() {
				//onReady 为uni-app支持的生命周期之一
		    	this.$refs.uForm.setRules(this.rules)
		},
		methods: {
			getAddressList() {
				listAddress({
					pageSize: 100000,
					userId: this.userId
				}).then(res => {
					this.addressList = res.rows
				})
			},
			closeBtn(){
				this.clearParams()
				this.popuoShow = false
			},
			confirmBtn(){
				this.$refs.uForm.validate().then(res => {
					addAddress(this.addressParams).then(res=>{
						this.getAddressList()
						this.clearParams()
						this.popuoShow = false
					})
				}).catch(errors => {
					
				})
			},
			clearParams(){
				this.addressParams.name = ''
				this.addressParams.phone = ''
				this.addressParams.address = ''
			},
			showPopup(){
				this.popuoShow = true
			},
			payChooseAddress(item){
				if(this.isChoose==1){
					this.chooseAddress = item
					console.log(this.chooseAddress);
				}
			},
			confirmChoose(){
				this.$noti.post('chooseAddress',this.chooseAddress)
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss">
	.address_page {
		padding-top: 20rpx;
		.address_list {
			display: flex;
			flex-direction: column;
			gap: 16rpx;
			justify-content: center;
			align-items: center;
			.address_item {
				background-color: #fff;
				border-radius: 12rpx;
				width: 95vw;
				padding: 18rpx;
				border: 1rpx solid #fff;
			}
			.choosed_item{
				border: 1rpx solid rgb(255,153,0);
				background-color: rgba(255,153,0,0.05);
				border-radius: 12rpx;
				width: 95vw;
				padding: 18rpx;
			}
		}
		.add_btn{
			position: fixed;
			bottom: 30rpx;
			width: 80vw;
			// height: 100rpx;
			display: flex;
			gap: 30rpx;
			left: 10vw;
		}
	}
</style>