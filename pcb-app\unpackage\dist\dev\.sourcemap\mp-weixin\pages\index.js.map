{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/index.vue?c34b", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/index.vue?5cbc", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/index.vue?a3c6", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/index.vue?65d8", "uni-app:///pages/index.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/index.vue?3645", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/index.vue?18a1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "swiperList", "baseUrl", "notiData", "onLoad", "methods", "getSwiperList", "pageSize", "res", "toGoodsCenter", "uni", "url", "buliding"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAinB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACsEroB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAC;MAAA;QACAC;UACA;QACA;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2a183b29&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2a183b29&\"", "var components\ntry {\n  components = {\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-swiper/u-swiper\" */ \"@/uni_modules/uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<u-swiper :list=\"swiperList\" indicator style=\"width: 95vw;margin: 20rpx auto;border-radius: 10rpx;\"\r\n\t\t   height=\"400rpx\"/> \r\n\t\t<!-- <u-notice-bar class=\"noti\" :text=\"notiData\" direction=\"column\" :disableTouch=\"true\"/> -->\r\n\t\t<view class=\"bar2\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<image src=\"../static/images/home/<USER>\"/>\r\n\t\t\t\t<text class=\"title\">A级板材</text>\r\n\t\t\t\t<!-- <text class=\"desc\">建滔/生益</text> -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<image src=\"../static/images/home/<USER>\"/>\r\n\t\t\t\t<text class=\"title\">极速出货</text>\r\n\t\t\t\t<!-- <text class=\"desc\">24小时出货</text> -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<button open-type=\"contact\" class=\"contact-button\">\r\n\t\t\t\t\t<image src=\"../static/images/home/<USER>\"/>\r\n\t\t\t\t\t<text class=\"title\">专业客服</text>\r\n\t\t\t\t\t<!-- <text class=\"desc\">24小时在线</text> -->\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"bar3\">\r\n\t\t\t<view class=\"item\" @click=\"buliding\">\r\n\t\t\t\t<view class=\"imageBg\">\r\n\t\t\t\t\t<image src=\"../static/images/home/<USER>\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<text class=\"title\">账号归属</text>\r\n\t\t\t\t\t<text class=\"dsc\">完成设置享更多福利</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @click=\"toGoodsCenter\">\r\n\t\t\t\t<view class=\"imageBg\">\r\n\t\t\t\t\t<image src=\"../static/images/home/<USER>\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<text class=\"title\">产品中心</text>\r\n\t\t\t\t\t<text class=\"dsc\">产品质量有保障</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"bar3\">\r\n\t\t\t<view class=\"item\" @click=\"buliding\">\r\n\t\t\t\t<view class=\"imageBg\">\r\n\t\t\t\t\t<image src=\"../static/images/home/<USER>\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<text class=\"title\">积分商城</text>\r\n\t\t\t\t\t<text class=\"dsc\">海量好礼免费换</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<button open-type=\"contact\" class=\"contact-button\">\r\n\t\t\t\t\t<view class=\"imageBg\">\r\n\t\t\t\t\t\t<image src=\"../static/images/home/<USER>\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t<text class=\"title\">在线客服</text>\r\n\t\t\t\t\t\t<text class=\"dsc\">专业客服在线答疑</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { listSwiper } from \"@/api/pcb/swiper.js\"\r\n\timport { baseUrl } from \"@/config.js\"\r\n  export default {\r\n    data() {\r\n      return {\r\n\t\t  swiperList:[],\r\n\t\t  baseUrl,\r\n\t\t  notiData:['通知1','通知2']\r\n      }\r\n    },\r\n\tonLoad() {\r\n\t\tthis.getSwiperList()\r\n\t},\r\n    methods: {\r\n\t\tgetSwiperList(){\r\n\t\t\tlistSwiper({pageSize:10000}).then(res=>{\r\n\t\t\t\tres.rows.forEach(item => {\r\n\t\t\t\t\tthis.swiperList.push(this.baseUrl+item.pic)\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t},\r\n\t\ttoGoodsCenter(){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:\"/pages/goodsCenter/goodsCenter\"\r\n\t\t\t})\r\n\t\t},\r\n\t\tbuliding(){\r\n\t\t\t// uni.showToast({\r\n\t\t\t// \ttitle:\"模块正在建设……\",\r\n\t\t\t// \ticon:\"none\"\r\n\t\t\t// })\r\n\t\t}\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.page{\r\n\t\tbackground-color: rgb(246,246,246);\r\n\t\tmin-height: 100vh;\r\n\t\t.noti{\r\n\t\t\twidth: 95vw;\r\n\t\t\tmargin: 10rpx auto;\r\n\t\t}\r\n\t\t.bar2{\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 15rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding: 20rpx 50rpx;\r\n\t\t\twidth: 95vw;\r\n\t\t\tmargin: 20rpx auto;\r\n\t\t\tbox-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t\t.item{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t.contact-button {\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\tpadding: 0;\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t\tbackground-color: transparent;\r\n\t\t\t\t\tline-height: normal;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\toutline: none;\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\timage{\r\n\t\t\t\t\twidth: 50rpx;\r\n\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.title{\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t\t.desc{\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\tcolor: darkgray;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.bar3{\r\n\t\t\twidth: 95vw;\r\n\t\t\tmargin: 20rpx auto;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\t.item{\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tpadding: 30rpx 10rpx 30rpx 20rpx;\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tbox-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t.contact-button {\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\tpadding: 0;\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t\tbackground-color: transparent;\r\n\t\t\t\t\tline-height: normal;\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\toutline: none;\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.imageBg{\r\n\t\t\t\t\tbackground-color: rgb(255,245,229);\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\twidth: 70rpx;\r\n\t\t\t\t\theight: 70rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\timage{\r\n\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.text{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\twidth: 220rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t.title{\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.dsc{\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881965\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}