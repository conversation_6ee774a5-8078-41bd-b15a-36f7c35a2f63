{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/payPage/payPage.vue?fe82", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/payPage/payPage.vue?8c08", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/payPage/payPage.vue?5691", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/payPage/payPage.vue?29f1", "uni-app:///pages/payPage/payPage.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/payPage/payPage.vue?bf9e", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/payPage/payPage.vue?8593"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderList", "address", "totalPrice", "onLoad", "methods", "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uni", "url", "<PERSON><PERSON><PERSON><PERSON>", "pay", "title", "icon", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACoFtpB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;UACAH;YACAI;YACAC;YACAC;cACAN;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxIA;AAAA;AAAA;AAAA;AAAqsC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACAztC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/payPage/payPage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/payPage/payPage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./payPage.vue?vue&type=template&id=428b9db6&\"\nvar renderjs\nimport script from \"./payPage.vue?vue&type=script&lang=js&\"\nexport * from \"./payPage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./payPage.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/payPage/payPage.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payPage.vue?vue&type=template&id=428b9db6&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.orderList, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.createTime.substring(0, 16)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payPage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payPage.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"pay_page\">\n\t\t<view class=\"choose_address\" @click=\"toChooseAddress\">\r\n\t\t\t<view class=\"choose_btn\" v-if=\"address == null\">\r\n\t\t\t\t点击选择地址\r\n\t\t\t</view>\r\n\t\t\t<view v-else @click=\"toChooseAddress\" class=\"address\">\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t收件人：{{address.name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t联系电话：{{address.phone}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t地址：{{address.address}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"orderList\">\r\n\t\t\t<view class=\"orderItem\" v-for=\"item in orderList\" :key=\"item.id\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<view @click=\"toOrderDetail(item.id)\">\r\n\t\t\t\t\t\t<view style=\"font-size: 32rpx;font-weight: bold;\">\r\n\t\t\t\t\t\t\t{{item.orderNo}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"32rpx\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"color: darkgray;\">\r\n\t\t\t\t\t\t{{item.createTime.substring(0,16)}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t基本信息:\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail_desc\">\r\n\t\t\t\t\t\t\t{{item.pcbWidth}}cm*{{item.pcbHeight}}cm {{item.layer}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t数量:\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail_desc\">\r\n\t\t\t\t\t\t\t{{item.num}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t型号:\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail_desc\">\r\n\t\t\t\t\t\t\t{{item.modelName}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t总价:\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail_desc\">\r\n\t\t\t\t\t\t\t￥{{item.totalPrice}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"jiesuan_btn\">\r\n\t\t\t<view style=\"width: 160rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;\">\r\n\t\t\t\t<text>总计：</text>\r\n\t\t\t\t<text style=\"color: rgb(255,153,0);font-weight: bold;\">￥{{totalPrice}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"height: 80rpx;\">\r\n\t\t\t\t<u-button @click=\"pay\" type=\"warning\" text=\"结算\"></u-button>\r\n\t\t\t</view>\r\n\t\t</view>\n\t</view>\n</template>\n\n<script>\r\n\timport { toast } from \"../../utils/common\"\nimport {pay} from \"@/api/pcb/order.js\"\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torderList:[],\r\n\t\t\t\taddress:null,\r\n\t\t\t\ttotalPrice:0\n\t\t\t}\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tlet pages = getCurrentPages()\r\n\t\t\tlet page = pages[pages.length-2]\r\n\t\t\tthis.orderList = page.data.chooseItem\r\n\t\t\tthis.$noti.add('chooseAddress',this.chooseAddress,this)\r\n\t\t\tthis.orderList.forEach(item=>{\r\n\t\t\t\tthis.totalPrice += item.totalPrice\r\n\t\t\t})\r\n\t\t},\n\t\tmethods: {\n\t\t\ttoChooseAddress(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:\"/pages/mine/myAddress/myAddress?a=\"+1\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchooseAddress(address){\r\n\t\t\t\tthis.address = address\r\n\t\t\t\tfor(let i=0;i<this.orderList.length;i++){\r\n\t\t\t\t\tthis.orderList[i].name = address.name\r\n\t\t\t\t\tthis.orderList[i].phone = address.phone\r\n\t\t\t\t\tthis.orderList[i].address = address.address\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpay(){\r\n\t\t\t\tif(this.address==null){\r\n\t\t\t\t\ttoast(\"请选择地址！\")\r\n\t\t\t\t\treturn\r\n\t\t\t\t}else{\r\n\t\t\t\t\tpay(this.orderList).then(res=>{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle:\"请耐心等待审核\",\r\n\t\t\t\t\t\t\ticon:\"success\",\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl:\"/pages/mine/index\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\r\n\t.pay_page{\r\n\t\tpadding-top: 20rpx;\r\n\t\t.choose_address{\r\n\t\t\twidth: 95vw;\r\n\t\t\theight: 150rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tmargin: 0 auto;\r\n\t\t\t.choose_btn{\r\n\t\t\t\twidth: 180rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tline-height: 100rpx;\r\n\t\t\t}\r\n\t\t\t.address{\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t\t.jiesuan_btn{\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\twidth: 100vw;\r\n\t\t\tpadding: 20rpx 40rpx;\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0;\r\n\t\t}\r\n\t\t.orderList{\r\n\t\t\tmargin-top: 40rpx;\r\n\t\t\t.orderItem{\r\n\t\t\t\twidth: 95vw;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tmargin: 20rpx auto;\r\n\t\t\t\tpadding: 30rpx 50rpx;\r\n\t\t\t\tbox-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t\t\t.title{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.2);\r\n\t\t\t\t\tview{\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tgap: 5rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.content{\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tgap: 20rpx;\r\n\t\t\t\t\t.detail{\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t.detail_title{\r\n\t\t\t\t\t\t\twidth: 150rpx;\r\n\t\t\t\t\t\t\tcolor: darkgray;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.detail_desc{\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t.up_btn{\r\n\t\t\t\t\t\t\t\tfont-weight: 100;\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tbackground-color: rgb(245,245,245);\r\n\t\t\t\t\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payPage.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./payPage.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881822\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}