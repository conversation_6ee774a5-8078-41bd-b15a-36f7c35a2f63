import request from '@/utils/request'

// 查询PCB列表
export function listGoods(query) {
  return request({
    url: '/pcb/goods/list',
    method: 'get',
    params: query
  })
}

// 查询PCB详细
export function getGoods(id) {
  return request({
    url: '/pcb/goods/' + id,
    method: 'get'
  })
}

// 新增PCB
export function addGoods(data) {
  return request({
    url: '/pcb/goods',
    method: 'post',
    data: data
  })
}

// 修改PCB
export function updateGoods(data) {
  return request({
    url: '/pcb/goods',
    method: 'put',
    data: data
  })
}

// 删除PCB
export function delGoods(id) {
  return request({
    url: '/pcb/goods/' + id,
    method: 'delete'
  })
}
