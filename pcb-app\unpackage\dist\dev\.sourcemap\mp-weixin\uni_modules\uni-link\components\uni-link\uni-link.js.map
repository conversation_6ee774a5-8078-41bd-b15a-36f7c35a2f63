{"version": 3, "sources": ["webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uni-link/components/uni-link/uni-link.vue?cc06", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uni-link/components/uni-link/uni-link.vue?51b1", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uni-link/components/uni-link/uni-link.vue?05f4", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uni-link/components/uni-link/uni-link.vue?0fe6", "uni-app:///uni_modules/uni-link/components/uni-link/uni-link.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uni-link/components/uni-link/uni-link.vue?0e16", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uni-link/components/uni-link/uni-link.vue?4e37"], "names": ["name", "props", "href", "type", "default", "text", "download", "showUnderLine", "copyTips", "color", "fontSize", "computed", "isShowA", "created", "methods", "isMail", "isTel", "openURL", "uni", "data", "content", "showCancel", "makePhoneCall", "phoneNumber"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqBrrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,eAaA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;EACA;EACAO;IACAC;MAIA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAYAC;QACAC;MACA;MACAD;QACAE;QACAC;MACA;IAEA;IACAC;MACAJ;QACAK;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAA48B,CAAgB,u2BAAG,EAAC,C;;;;;;;;;;;ACAh+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-link/components/uni-link/uni-link.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-link.vue?vue&type=template&id=6c93f7f9&\"\nvar renderjs\nimport script from \"./uni-link.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-link.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-link.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-link/components/uni-link/uni-link.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=template&id=6c93f7f9&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=script&lang=js&\"", "<template>\n\t<a v-if=\"isShowA\" class=\"uni-link\" :href=\"href\"\n\t\t:class=\"{'uni-link--withline':showUnderLine===true||showUnderLine==='true'}\"\n\t\t:style=\"{color,fontSize:fontSize+'px'}\" :download=\"download\">\n\t\t<slot>{{text}}</slot>\n\t</a>\n\t<!-- #ifndef APP-NVUE -->\n\t<text v-else class=\"uni-link\" :class=\"{'uni-link--withline':showUnderLine===true||showUnderLine==='true'}\"\n\t\t:style=\"{color,fontSize:fontSize+'px'}\" @click=\"openURL\">\n\t\t<slot>{{text}}</slot>\n\t</text>\n\t<!-- #endif -->\n\t<!-- #ifdef APP-NVUE -->\n\t<text v-else class=\"uni-link\" :class=\"{'uni-link--withline':showUnderLine===true||showUnderLine==='true'}\"\n\t\t:style=\"{color,fontSize:fontSize+'px'}\" @click=\"openURL\">\n\t\t{{text}}\n\t</text>\n\t<!-- #endif -->\n</template>\n\n<script>\n\t/**\n\t * Link 外部网页超链接组件\n\t * @description uni-link是一个外部网页超链接组件，在小程序内复制url，在app内打开外部浏览器，在h5端打开新网页\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=1182\n\t * @property {String} href 点击后打开的外部网页url\n\t * @property {String} text 显示的文字\n\t * @property {String} downlaod H5平台下载文件名\n\t * @property {Boolean} showUnderLine 是否显示下划线\n\t * @property {String} copyTips 在小程序端复制链接时显示的提示语\n\t * @property {String} color 链接文字颜色\n\t * @property {String} fontSize 链接文字大小\n\t * @example * <uni-link href=\"https://ext.dcloud.net.cn\" text=\"https://ext.dcloud.net.cn\"></uni-link>\n\t */\n\texport default {\n\t\tname: 'uniLink',\n\t\tprops: {\n\t\t\thref: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\ttext: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tdownload: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tshowUnderLine: {\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tcopyTips: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '已自动复制网址，请在手机浏览器里粘贴该网址'\n\t\t\t},\n\t\t\tcolor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#999999'\n\t\t\t},\n\t\t\tfontSize: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 14\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tisShowA() {\n\t\t\t\t// #ifdef H5\n\t\t\t\tthis._isH5 = true;\n\t\t\t\t// #endif\n\t\t\t\tif ((this.isMail() || this.isTel()) && this._isH5 === true) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis._isH5 = null;\n\t\t},\n\t\tmethods: {\n\t\t\tisMail() {\n\t\t\t\treturn this.href.startsWith('mailto:');\n\t\t\t},\n\t\t\tisTel() {\n\t\t\t\treturn this.href.startsWith('tel:');\n\t\t\t},\n\t\t\topenURL() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tif (this.isTel()) {\n\t\t\t\t\tthis.makePhoneCall(this.href.replace('tel:', ''));\n\t\t\t\t} else {\n\t\t\t\t\tplus.runtime.openURL(this.href);\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5\n\t\t\t\twindow.open(this.href)\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: this.href\n\t\t\t\t});\n\t\t\t\tuni.showModal({\n\t\t\t\t\tcontent: this.copyTips,\n\t\t\t\t\tshowCancel: false\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tmakePhoneCall(phoneNumber) {\n\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\tphoneNumber\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* #ifndef APP-NVUE */\n\t.uni-link {\n\t\tcursor: pointer;\n\t}\n\n\t/* #endif */\n\t.uni-link--withline {\n\t\ttext-decoration: underline;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541877333\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}