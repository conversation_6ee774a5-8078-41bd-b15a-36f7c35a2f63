{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/App.vue?818a", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/App.vue?0731", "uni-app:///App.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/App.vue?77c2", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/App.vue?dd81"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "mixin", "share", "use", "uView", "plugins", "config", "productionTip", "prototype", "$store", "store", "$noti", "noti", "App", "mpType", "app", "$mount", "onLaunch", "methods", "initApp", "initConfig", "checkLogin"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AAAoC;AAAA;AARpC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAS1DC,YAAG,CAACC,KAAK,CAACC,cAAK,CAAC;AAChBF,YAAG,CAACG,GAAG,CAACC,gBAAK,CAAC;AACdJ,YAAG,CAACG,GAAG,CAACE,gBAAO,CAAC;AAEhBL,YAAG,CAACM,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCP,YAAG,CAACQ,SAAS,CAACC,MAAM,GAAGC,cAAK;AAC5BV,YAAG,CAACQ,SAAS,CAACG,KAAK,GAAGC,qBAAI;AAC1BC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAIf,YAAG,mBACda,YAAG,EACN;AAEF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACxBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAgmB,CAAgB,6mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACCpnB;AACA;AACA;AAAA,eAEA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;IAIA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAA2oC,CAAgB,4lCAAG,EAAC,C;;;;;;;;;;;ACA/pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\r\nimport App from './App'\r\nimport store from './store' // store\r\nimport plugins from './plugins' // plugins\r\nimport './permission' // permission\r\nimport uView from '@/uni_modules/uview-ui'\r\nimport noti from '@/utils/notification.js'\r\nimport share from '@/utils/share.js'\r\n\r\nVue.mixin(share)\r\nVue.use(uView)\r\nVue.use(plugins)\r\n\r\nVue.config.productionTip = false\r\nVue.prototype.$store = store\r\nVue.prototype.$noti = noti\r\nApp.mpType = 'app'\r\n\r\nconst app = new Vue({\r\n  ...App\r\n})\r\n\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n  import config from './config'\r\n  import store from '@/store'\r\n  import { getToken } from '@/utils/auth'\r\n\r\n  export default {\r\n    onLaunch: function() {\r\n      this.initApp()\r\n    },\r\n    methods: {\r\n      // 初始化应用\r\n      initApp() {\r\n        // 初始化应用配置\r\n        this.initConfig()\r\n        // 检查用户登录状态\r\n        //#ifdef H5\r\n        this.checkLogin()\r\n        //#endif\r\n      },\r\n      initConfig() {\r\n        this.globalData.config = config\r\n      },\r\n      checkLogin() {\r\n        if (!getToken()) {\r\n          this.$tab.reLaunch('/pages/login') \r\n        }\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  @import \"@/uni_modules/uview-ui/index.scss\";\r\n  @import '@/static/scss/index.scss'\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541883119\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}