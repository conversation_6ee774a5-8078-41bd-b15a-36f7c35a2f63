<template>
	<page-meta :page-style="'overflow:'+(choosePopupShow||chooseNameShow||allCheckPopupShow?'hidden':'visible')"></page-meta>
	<view class="page">
		<div class="topBack"></div>
		<uni-collapse class="coll" v-model="collapseValue">
			<uni-collapse-item titleBorder="none" class="collItem">
				<template v-slot:title>
					<text class="title">基本信息</text>
				</template>
				<view class="content">
					<view class="contentItem">
						<view class="item">
							<text class="label">型号</text>
							<view style="background-color: rgb(239,239,239);padding: 10rpx;border-radius: 8rpx;">
								<input v-model="params.modelName" placeholder="请输入型号" style="background-color: #fff;padding: 5rpx;" />
							</view>
						</view>
						<view class="item">
							<text class="label">板材类型</text>
							<view class="params" @click="nameChoose">
								<text v-if="params.type">{{params.type}}</text>
								<text v-else style="color: darkgray;">请选择板材类型</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
						<view class="item">
							<view class="label">
								板子层数
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="layerChoose">
								<text v-if="params.layer">{{params.layer}}</text>
								<text v-else style="color: darkgray;">请选择板子层数</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
						<view class="item">
							<view class="label">
								板子大小
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="paramsInput">
								<input @input="pcbWidthInput" placeholder="长度/x" v-model="params.pcbWidth" />
								<view style="width: 80rpx;">x</view>
								<input @input="pcbHeightInput" placeholder="宽度/y" v-model="params.pcbHeight" />
								<view style="position:100rpx;">mm</view>
							</view>
						</view>
						<view class="item">
							<view class="label">
								拼版款数
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="paramsInput">
								<input type="number" :disabled="isPCS" @input="styleCount1Input" placeholder="请输入" v-model="params.styleCount1" />
								<view style="width: 80rpx;">x</view>
								<input type="number" :disabled="isPCS" @input="styleCount2Input" placeholder="请输入" v-model="params.styleCount2" />
							</view>
						</view>
						<view class="item">
							<view class="label" style="display: flex;justify-content: space-between;">
								<view class="">
									板子数量
									<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
								</view>
							</view>
							<view class="params" @click="numChoose">
								<text v-if="params.num">{{params.num}}</text>
								<text v-else style="color: darkgray;">请选择板子数量</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
						<view class="item">
							<view class="label">
								出货方式
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="outChoose">
								<text v-if="params.outWay">{{params.outWay}}</text>
								<text v-else style="color: darkgray;">请选择出货方式</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
				</view>
			</uni-collapse-item>
			<uni-collapse-item titleBorder="none" class="collItem">
				<template v-slot:title>
					<text class="title">工艺信息</text>
				</template>
				<view class="content">
					<view class="contentItem">
						<view class="item">
							<view class="label">
								板子厚度
							</view>
							<view class="params" @click="thicknessChoose">
								<text v-if="params.thickness">{{params.thickness}}</text>
								<text v-else style="color: darkgray;">请选择板子厚度</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
					<view class="contentItem">
						<view class="item">
							<view class="label">
								铜箔厚度
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="cuThicknessChoose">
								<text v-if="params.cuThickness">{{params.cuThickness}}</text>
								<text v-else style="color: darkgray;">请选择铜箔厚度</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
					<view class="contentItem">
						<view class="item">
							<view class="label">
								阻焊颜色
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="weldColorChoose">
								<view v-if="params.weldColor" style="display: flex;gap: 5rpx;align-items: center;">
									{{params.weldColor}}
								</view>
								<text v-else style="color: darkgray;">请选择阻焊颜色</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
					<view class="contentItem">
						<view class="item">
							<view class="label">
								字符颜色
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="charColorChoose">
								<view v-if="params.charColor" style="display: flex;gap: 5rpx;align-items: center;">
									{{params.charColor}}
								</view>
								<text v-else style="color: darkgray;">请选择字符颜色</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
					<view class="contentItem">
						<view class="item">
							<view class="label">
								阻焊覆盖
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="coverChoose">
								<text v-if="params.cover">{{params.cover}}</text>
								<text v-else style="color: darkgray;">请选择</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
					<view class="contentItem">
						<view class="item">
							<view class="label">
								测试方式
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="testChoose">
								<text v-if="params.test">{{params.test}}</text>
								<text v-else style="color: darkgray;">请选择测试方式</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
					<view class="contentItem">
						<view class="item">
							<view class="label">
								焊盘喷镀
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="surfaceChoose">
								<text v-if="params.surface">{{params.surface}}</text>
								<text v-else style="color: darkgray;">请选择</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
					<view class="contentItem" v-if="!JSON.parse(choosePriceModel.thermalConductivity).map(item=>item.name).includes('不显示')">
						<view class="item">
							<view class="label">
								导热
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="thermalConductivityChoose">
								<text v-if="params.thermalConductivity">{{params.thermalConductivity}}</text>
								<text v-else style="color: darkgray;">请选择</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
					<view class="contentItem" v-if="!JSON.parse(choosePriceModel.molding).map(item=>item.name).includes('不显示')">
						<view class="item">
							<view class="label">
								成型方式
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="moldingChoose">
								<text v-if="params.molding">{{params.molding}}</text>
								<text v-else style="color: darkgray;">请选择</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
				</view>
			</uni-collapse-item>
			<uni-collapse-item titleBorder="none" class="collItem">
				<template v-slot:title>
					<text class="title">个性化服务</text>
				</template>
				<view class="content">
					<view class="contentItem">
						<view class="item">
							<view class="label">
								开票方式
								<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
							</view>
							<view class="params" @click="billTypeChoose">
								<text v-if="params.billType">{{params.billType}}</text>
								<text v-else style="color: darkgray;">请选择</text>
								<u-icon name="arrow-right" size="15"></u-icon>
							</view>
						</view>
					</view>
					<!-- <view class="item">
						<view class="label">
							订单交期
							<image class="wenhao" src="../../static/images/work/wenhao.png"></image>
						</view>
						<view class="params" @click="deliveryDateChoose">
							<text v-if="params.deliveryDate">{{params.deliveryDate}}</text>
							<text v-else style="color: darkgray;">请选择订单交期</text>
							<u-icon name="arrow-right" size="15"></u-icon>
						</view>
					</view> -->
				</view>
			</uni-collapse-item>
		</uni-collapse>
		
		
		
		<u-popup :show="chooseNameShow" @close="chooseNamePopupClose"
		mode="bottom" round="10">
		    <view style="height: 50vh;background-color: rgb(247,245,242);border-top-right-radius: 16rpx;border-top-left-radius: 16rpx;">
				<view style="display: flex;justify-content: space-between;padding: 16rpx 30rpx;">
					<text style="color: rgba(0, 0, 0, 0.4);font-size: 32rpx;" @click="chooseNamePopupClose">取消</text>
					<text style="font-size: 36rpx;font-weight: bold;">板材类别</text>
					<text style="color: rgb(255,153,0);font-size: 32rpx;" @click="chooseNameConfirm">确定</text>
				</view>
		        <view style="display: flex;width: 95vw;margin: 0 auto;justify-content: space-between;
				    justify-content: space-between;flex-wrap: wrap;margin-top: 20rpx;">
		        	<view @click="chooseType(item)" v-for="item in typeList" :key="item.id" :style="choosedType.id == item.id ? 'border:1rpx solid rgb(255,153,0);background-color: rgba(255,153,0,0.05);box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);':''" style="width: 46vw;background-color: #fff;display: flex;
					     flex-direction: column;align-items: center;padding: 10rpx;margin-bottom: 20rpx;border-radius: 12rpx;box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
						 position: relative;border:1rpx solid #fff;">
		        		<image :src="baseUrl + item.pic" style="height: 100rpx;width: 200rpx;"/>
						<text style="margin-top: 10rpx;">{{item.name}}</text>
						<div style="position: absolute;right: 10rpx;top: 10rpx;">
							<div v-if="choosedType.id != item.id" style="border-radius: 50%;width: 30rpx;height: 30rpx;border: 1rpx solid darkgray;"></div>
							<image v-else src="../../static/images/work/selected.png" style="width: 30rpx;height: 30rpx;"></image>
						</div>
		        	</view>
		        </view>
		    </view>
		</u-popup>
		
		<u-popup :show="choosePopupShow" @close="choosePopupClose" mode="bottom" round="10">
		    <scroll-view scroll-y="true" style="background-color: rgb(247,245,242);border-top-right-radius: 16rpx;border-top-left-radius: 16rpx;" :style="{height:popupHeight+'vh'}">
				<view style="display: flex;justify-content: space-between;padding: 16rpx 30rpx;">
					<text style="color: rgba(0, 0, 0, 0.4);font-size: 32rpx;" @click="choosePopupClose">取消</text>
					<text style="font-size: 36rpx;font-weight: bold;">{{popupTitle}}</text>
					<text style="color: rgb(255,153,0);font-size: 32rpx;" @click="choosePopupConfirm">确定</text>
				</view>
		        <view style="display: flex;width: 95vw;margin: 0 auto;justify-content: space-between;
				    justify-content: flex-start;flex-wrap: wrap;margin-top: 20rpx;gap: 10rpx;">
		        	<view @click="choosePopup(item)" v-for="item,index in popupData" :key="index" :style="chooseData[popupKey] == item.text ? 'border:1rpx solid rgb(255,153,0);background-color: rgba(255,153,0,0.05);box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);padding: 10rpx '+padding+'rpx;':'padding: 10rpx '+padding+'rpx;'" style="background-color: #fff;display: flex;align-items: center;
					margin-bottom: 10rpx;border-radius: 6rpx;box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);justify-content: center;	
						 position: relative;text-align: center;border: 1rpx solid #fff;flex-grow: 1;">
						<text style="min-width: 80rpx;">{{item.text}}</text>
						<image style="width: 40rpx;height: 40rpx;position: absolute;right: 0;bottom: 0;" src="../../static/images/work/selected2.png" v-if="chooseData[popupKey] == item.text" />
		        	</view>
		        </view>
				<view class="" v-if="popupKey == 'num'">
					<text style="margin-left: 2.5vw;">其他数量:</text>
					<input @input="numInput" type="number" style="display: block;height: 60rpx;border-radius: 6rpx;;font-size: 24rpx;background-color: #fff;width: 90vw;margin: 20rpx auto;padding: 16rpx 20rpx;" placeholder="请输入100的倍数" />
					<text style="margin-left: 2.5vw;color: rgba(255,153,0,0.6);font-size: 30rpx;">* 数量必须是100的倍数</text>
				</view>
		    </scroll-view>
		</u-popup>
		
		<!-- <u-popup :show="allCheckPopupShow" @close="allCheckPopupClose" mode="bottom" round="10">
		    <view style="background-color: rgb(247,245,242);border-top-right-radius: 16rpx;border-top-left-radius: 16rpx;" :style="{height:popupHeight+'vh'}">
				<view style="display: flex;justify-content: space-between;padding: 16rpx 30rpx;">
					<text style="color: rgba(0, 0, 0, 0.4);font-size: 32rpx;" @click="choosePopupClose">取消</text>
					<text style="font-size: 36rpx;font-weight: bold;">{{popupTitle}}</text>
					<text style="color: rgb(255,153,0);font-size: 32rpx;" @click="choosePopupConfirm">确定</text>
				</view>
		        <view style="display: flex;width: 95vw;margin: 0 auto;justify-content: space-between;
				    justify-content: flex-start;flex-wrap: wrap;margin-top: 20rpx;gap: 10rpx;">
		        	<view @click="allChoosePopup(item)" v-for="item,index in popupData" :key="index" :style="chooseData[popupKey].includes(item.text) ? 'border:1rpx solid rgb(255,153,0);background-color: rgba(255,153,0,0.05);box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);padding: 10rpx '+padding+'rpx;':'padding: 10rpx '+padding+'rpx;'" style="background-color: #fff;display: flex;align-items: center;
					margin-bottom: 10rpx;border-radius: 6rpx;box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);justify-content: center;	
						 position: relative;text-align: center;border: 1rpx solid #fff;flex-grow: 1;">
						<div :style="{backgroundColor:item.color}" style="display: inline-block;border: 1rpx solid darkgray;border-radius: 50%;width: 20rpx;height: 20rpx;" v-if="item.color"></div>
						<text style="min-width: 80rpx;">{{item.text}}</text>
						<image style="width: 40rpx;height: 40rpx;position: absolute;right: 0;bottom: 0;" src="../../static/images/work/selected2.png" v-if="chooseData[popupKey].includes(item.text)" />
		        	</view>
		        </view>
		    </view>
		</u-popup> -->
		
		<view class="bottomBtn">
			<view class="">
				<u-button type="info" text="重置" @click="chongzhi" />
			</view>
			<view class="">
				<u-button type="warning" :text="params.isBack==1?'返单':'立即计价'" @click="next" />
			</view>
		</view>
	</view>
</template>

<script>
	import { baseUrl } from "../../config"
	import {listType} from "@/api/pcb/type.js"
	import {toast} from "@/utils/common.js"
	import {listPriceModel} from "@/api/pcb/priceModel.js"
	export default {
		data() {
			return {
				collapseValue:['0'],
				chooseNameShow:false,
				baseUrl,
				params:{
					layer:'',
					type:'',
					typeId:'',
					num:'',
					thickness:'',
					cuThickness:'',
					test:'',
					charColor:'',
					weldColor:'',
					cover:'',
					molding:'',
					surface:'',
					outWay:'',
					pcbWidth:'',
					pcbHeight:'',
					area:'',
					price:'',
					deliveryDate:'',
					billType:'',
					thermalConductivity:'',
					styleCount1:1,
					styleCount2:1,
					modelName:'',
					isBack:2
				},
				typeList:[],
				choosedType:{},
				choosePopupShow:false,
				allCheckPopupShow:false,
				popupTitle:'',
				popupData:[],
				chooseData:{
					layer:'',
					num:'',
					thickness:'',
					cuThickness:'',
					test:'',
					charColor:'',
					weldColor:'',
					cover:'',
					molding:'',
					surface:'',
					outWay:'',
					deliveryDate:'',
					billType:'',
					thermalConductivity:''
				},
				popupKey:'',
				padding:45,
				popupHeight:40,
				priceModel:[],
				layerList:[],
				choosePriceModel:null,
				isPCS:false
			}
		},
		onLoad() {
			listType({pageSize:100000}).then(res=>{
				this.typeList = res.rows
			})
			this.$noti.add("addWorkParams",this.setParams,this)
		},
		methods: {
			nameChoose(){
				this.getGoodsList()
				this.chooseNameShow = true
			},
			chongzhi(){
				let paramsKey = Object.keys(this.params)
				this.params.isBack = 2
				paramsKey.forEach(key=>{
					this.params[key] = ''
					this.chooseData[key] = ''
				})
			},
			chooseNamePopupClose(){
				this.chooseNameShow = false
			},
			getGoodsList(){
				listType({pageSize:100000}).then(res=>{
					this.typeList = res.rows
				})
			},
			chooseType(item){
				this.choosedType = item
				listPriceModel({pageSize:100000,typeId:item.id}).then(res=>{
					this.priceModel = res.rows
					this.layerList = [...new Set(this.priceModel.map(item => item.layer))]
				})
			},
			chooseNameConfirm(){
				this.params.type = this.choosedType.name
				this.params.typeId = this.choosedType.id
				this.changeChoosePriceModel()
				this.chooseNameShow = false
			},
			choosePopupClose(){
				this.choosePopupShow = false
				this.allCheckPopupShow = false
			},
			choosePopupConfirm(){
				if(this.popupKey == 'surface'){
					this.params.auPdNi = this.chooseData.auPdNi
					this.params.au = this.chooseData.au
				}
				if(this.popupKey == 'layer'){
					this.choosePriceModel = this.priceModel.filter(item=>item.layer == this.chooseData[this.popupKey])[0]
				}
				if(this.popupKey == 'num'){
					const texts = this.popupData.map(item=>item.text)
					if(!texts.includes(this.chooseData.num)&&parseInt(this.chooseData.num)%100!=0){
						uni.showToast({
							title:"请输入100的倍数！",
							icon:"none"
						})
						return
					}
				}
				if(this.popupKey != 'key'){
					this.params.isBack = 2
				}
				if(this.popupKey == 'outWay'){
					if(this.chooseData[this.popupKey] == '单片出货'){
						this.isPCS = true
						this.params.styleCount1 = 1
						this.params.styleCount2 = 1
					}else{
						this.isPCS = false
					}
				}
				this.params[this.popupKey] = this.chooseData[this.popupKey]
				this.choosePopupShow = false
				this.allCheckPopupShow = false
				let keys = ['layer','num','outWay']
				if(keys.includes(this.popupKey)){
					this.changeChoosePriceModel()
				}
			},
			allCheckPopupClose(){
				this.allCheckPopupShow = false
			},
			layerChoose(){
				this.popupData = []
				this.popupHeight = 60
				this.layerList.sort()
				for(let layer of this.layerList){
					let temp = {
						text:layer
					}
					this.popupData.push(temp)
				}
				this.popupTitle = '板子层数'
				this.choosePopupShow = true
				this.popupKey = 'layer'
			},
			choosePopup(item){
				this.chooseData[this.popupKey] = item.text
			},
			allChoosePopup(item){
				let tempArr = []
				if(this.chooseData[this.popupKey] != ''){
					tempArr = this.chooseData[this.popupKey].split(',')
				}
				if(tempArr.includes(item.text)){
					tempArr.splice(tempArr.indexOf(item.text),1)
				}else{
					tempArr.push(item.text)
				}
				this.chooseData[this.popupKey] = tempArr.join(',')
			},
			numInput(e){
				this.chooseData.num = parseInt(e.detail.value)
			},
			numChoose(){
				let numList = [3,5,10,15,20,25,30,50,75,100,125,150,200,250,300,350,400,450,500,600,700,800,900,1000,1200,1500,1600,1750,2000,2500,3000,3500,4000,4500,5000]
				this.initPopupData(numList,'num','板子数量',30,80)
			},
			thicknessChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				let thickness = JSON.parse(this.choosePriceModel.thickness)
				for(let i=0;i<thickness.length;i++){
					thickness[i] = thickness[i].toFixed(1)
				}
				this.initPopupData(thickness,'thickness','板子厚度',30,40)
			},
			cuThicknessChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				let cuThickness = JSON.parse(this.choosePriceModel.cuThickness).map(item=>item.name)
				this.initPopupData(cuThickness,'cuThickness','铜箔厚度(外层)',30,25)
			},
			deliveryDateChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				let deliveryDate = JSON.parse(this.choosePriceModel.deliveryDate).map(item=>item.name)
				this.initPopupData(deliveryDate,'deliveryDate','订单交期',30,25)
			},
			testChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				let test = JSON.parse(this.choosePriceModel.test).map(item=>item.name)
				this.initPopupData(test,'test','测试方式',30,25)
			},
			outChoose(){
				// let outWay = JSON.parse(this.choosePriceModel.outWay).map(item=>item.name)
				let outWay = ['单片出货','SET出货']
				this.initPopupData(outWay,'outWay','出货方式',30,40)
			},
			charColorChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				this.popupData = []
				let text = JSON.parse(this.choosePriceModel.charColor).map(item=>item.name)
				for(let i=0;i<text.length;i++){
					let temp = {
						text:text[i],
					}
					this.popupData.push(temp)
				}
				this.popupTitle = '字符颜色'
				this.padding = 30
				this.popupHeight = 25
				this.popupKey = 'charColor'
				this.choosePopupShow = true
			},
			weldColorChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				this.popupData = []
				let text = JSON.parse(this.choosePriceModel.weldColor).map(item=>item.name)
				for(let i=0;i<text.length;i++){
					let temp = {
						text:text[i],
					}
					this.popupData.push(temp)
				}
				this.popupTitle = '阻焊颜色'
				this.padding = 20
				this.popupHeight = 40
				this.popupKey = 'weldColor'
				this.choosePopupShow = true
			},
			coverChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				let cover = JSON.parse(this.choosePriceModel.cover).map(item=>item.name)
				this.initPopupData(cover,'cover','阻焊覆盖',20,40)
			},
			moldingChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				let molding = JSON.parse(this.choosePriceModel.molding).map(item=>item.name)
				this.initPopupData(molding,'molding','成型方式',50,30)
			},
			surfaceChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				let surface = JSON.parse(this.choosePriceModel.surface).map(item=>item.name)
				this.initPopupData(surface,'surface','焊盘喷镀',30,60)
			},
			billTypeChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				let billType = JSON.parse(this.choosePriceModel.billType).map(item=>item.name)
				this.initPopupData(billType,'billType','开票方式',30,60)
			},
			thermalConductivityChoose(){
				if(this.choosePriceModel == null){
					uni.showToast({
						title:"请先填写基础基本信息",
						icon:'none'
					})
					return
				}
				let thermalConductivity = JSON.parse(this.choosePriceModel.thermalConductivity).map(item=>item.name)
				this.initPopupData(thermalConductivity,'thermalConductivity','导热',30,60)
			},
			initPopupData(arr,key,title,padding,height){
				this.popupData = []
				this.popupTitle = title
				this.padding = padding
				this.popupHeight = height
				arr.forEach(item=>{
					let temp = {
						text:item
					}
					this.popupData.push(temp)
				})
				this.popupKey = key
				this.choosePopupShow = true
			},
			next(){
				const checks = [
					{ condition: this.params.type === '', message:'请选择板材类型'},
					{ condition: this.params.layer === '', message:'请选择板子层数'},
					{ condition: this.params.pcbWidth === '' || this.params.pcbHeight === '', message:'请输入板子大小'},
					{ condition: this.params.styleCount1 === '' || this.params.styleCount2 === '', message:'请输入拼版款数'},
					{ condition: this.params.num === '', message:'请选择板子数量'},
					{ condition: this.params.outWay === '', message:'请选择出货方式'},
					{ condition: this.params.thickness === '', message:'请选择板子厚度'},
					{ condition: this.params.cuThickness === '', message:'请选择铜箔厚度'},
					{ condition: this.params.weldColor === '', message:'请选择阻焊颜色'},
					{ condition: this.params.charColor === '', message:'请选择字符颜色'},
					{ condition: this.params.cover === '', message:'请选择阻焊覆盖'},
					{ condition: this.params.test === '', message:'请选择测试方式'},
					{ condition: this.params.surface === '', message:'请选择焊盘喷镀'},
					{ condition: this.params.billType === '', message:'请选择开票方式'},
				]
				for(let check of checks){
					if(check.condition){
						toast(check.message)
						return
					}
				}
				if(!JSON.parse(this.choosePriceModel.thermalConductivity).map(item=>item.name).includes('不显示') && this.params.thermalConductivity === ''){
					toast("请选择导热")
					return
				}
				if(!JSON.parse(this.choosePriceModel.molding).map(item=>item.name).includes('不显示') && this.params.molding === ''){
					toast("请选择成型方式")
					return
				}
				uni.navigateTo({
					url:"/pages/jiesuan/jiesuan"
				})
			},
			changeChoosePriceModel(){
				this.params.isBack = 2
				if(this.params.type == null || this.params.layer == null || this.params.pcbHeight == null || this.params.pcbWidth == null || this.params.styleCount1 == null || this.params.styleCount2 == null || this.params.num == null || this.params.outWay == null ||
				  this.params.type == '' || this.params.layer == '' || this.params.pcbHeight == '' || this.params.pcbWidth == '' || this.params.styleCount1 == '' || this.params.styleCount2 == '' || this.params.num == '' || this.params.outWay == ''){
					  this.choosePriceModel = null
				  }else{
					  let totalArea = (this.params.pcbHeight*this.params.pcbWidth*this.params.num)/1000000
					  if(this.params.outWay == 'SET出货'){
						  totalArea = totalArea/(this.params.styleCount1*this.params.styleCount2)
					  }
					  console.log(totalArea);
					  let type = this.typeList.filter(item => item.id == this.params.typeId)[0]
					  for(let i=0;i<type.tblTypeItemList.length;i++){
						  if(type.tblTypeItemList[i].maxArea!=null&&totalArea>type.tblTypeItemList[i].minArea&&totalArea<=type.tblTypeItemList[i].maxArea){
							  listPriceModel({typeItemId:type.tblTypeItemList[i].id,typeId:this.params.typeId,layer:this.params.layer}).then(res=>{
								  this.choosePriceModel = res.rows[0]
							  })
						  }else if(type.tblTypeItemList[i].maxArea == null && totalArea > type.tblTypeItemList[i].minArea){
							  listPriceModel({typeItemId:type.tblTypeItemList[i].id,typeId:this.params.typeId,layer:this.params.layer}).then(res=>{
							  	  this.choosePriceModel = res.rows[0]
							  })
						  }
					  }
				  }
			},
			pcbWidthInput(e){
				this.changeChoosePriceModel()
			},
			pcbHeightInput(e){
				this.changeChoosePriceModel()
			},
			styleCount1Input(e){
				this.changeChoosePriceModel()
			},
			styleCount2Input(e){
				this.changeChoosePriceModel()
			},
			setParams(data){
				this.params = data
				this.chooseData = data
				this.changeChoosePriceModel()
				this.params.isBack = 1
				console.log(data);
			}
		},
	}
</script>

<style lang="scss">
	.page{
		padding-bottom: 130rpx;
		min-height: 100vh;
		padding-top: 1rpx;
		.topBack{
			background-color: rgb(147,116,84);
			width: 100vw;
			height: 280rpx;
			position: absolute;
			z-index: -999;
			border-radius: 100% / 0 0 50% 50%;
		}
		.coll{
			width: 95vw;
			margin: 20rpx auto;
			background-color: rgba(255, 255, 255, 0);
			.title{
				border-left: 8rpx solid rgb(255,153,0);
				padding-left: 20rpx;
				font-weight: bold;
				font-size: 30rpx;
				margin-left: 20rpx;
			}
			.collItem{
				padding: 20rpx 10rpx 20rpx 10rpx;
				margin-top: 20rpx;
				background-color: #fff;
				border-radius: 12rpx;
				.content{
					padding: 20rpx;
					.item{
						padding: 20rpx 0;
						border-bottom: 1rpx solid rgba(0, 0, 0, 0.2);
						.label{
							display: flex;
							align-items: center;
							.wenhao{
								width: 30rpx;
								height: 30rpx;
								margin-left: 5rpx;
							}
						}
						.params{
							display: flex;
							justify-content: space-between;
							border: 1rpx solid rgba(0, 0, 0, 0.1);
							padding: 16rpx;
							border-radius: 6rpx;
							margin-top: 10rpx;
							color: rgb(255,153,0);
						}
						.paramsInput{
							margin-top: 10rpx;
							padding: 6rpx;
							display: flex;
							border-radius: 6rpx;
							background-color: rgb(239,239,239);
							input{
								width: 250rpx;
								background-color: #fff;
								padding: 10rpx 15rpx;
								color: rgb(255,153,0);
							}
							view{
								width: 100rpx;
								background-color: rgb(245,245,245);
								text-align: center;
								display: flex;
								justify-content: center;
								align-items: center;
								color: rgba(0, 0, 0, 0.6);
							}
						}
					}
				}
			}
		}
		.bottomBtn{
			position: fixed;
			bottom: 0;
			width: 100vw;
			background-color: rgb(251,249,248);
			height: 120rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			padding: 20rpx 2.5vw;
			gap: 30rpx;
			z-index: 999;
			view{
				flex-grow: 1;
			}
		}
	}
</style>