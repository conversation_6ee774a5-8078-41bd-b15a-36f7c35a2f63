{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/xieyi/xieyi.vue?94f8", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/xieyi/xieyi.vue?e044", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/xieyi/xieyi.vue?148d", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/xieyi/xieyi.vue?e11a", "uni-app:///pages/xieyi/xieyi.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/xieyi/xieyi.vue?492e", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/xieyi/xieyi.vue?d5e3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCAppB;AAAA,2B;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAA2tC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACA/uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/xieyi/xieyi.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/xieyi/xieyi.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./xieyi.vue?vue&type=template&id=7cc02910&scoped=true&\"\nvar renderjs\nimport script from \"./xieyi.vue?vue&type=script&lang=js&\"\nexport * from \"./xieyi.vue?vue&type=script&lang=js&\"\nimport style0 from \"./xieyi.vue?vue&type=style&index=0&id=7cc02910&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7cc02910\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/xieyi/xieyi.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieyi.vue?vue&type=template&id=7cc02910&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieyi.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieyi.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"fui-wrap\">\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"subtitle\">一、信息收集范围和目的</view>\r\n\t\t\t<view class=\"\">\r\n\t\t\t\t当您使用我们的微信小程序时，我们可能会收集您的个人信息，包括但不限于昵称、头像、手机号码等。这些信息将用于以下目的\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content-item\">\r\n\t\t\t\t1. 为您提供更好的服务，例如获取昵称和手机号信息以便我们为您提供服务。\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content-item\">\r\n\t\t\t\t2. 在您提交入驻申请时我们会向您要姓名、手机号等信息以便我们处理您的订单和请求。\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content-item\">\r\n\t\t\t\t3. 维护和改善我们的产品和服务质量。\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"subtitle\">二、信息使用与共享</view>\r\n\t\t\t<view class=\"content-item\">\r\n\t\t\t\t本公司不会与其他的任何公司、组织和个人分享您的用户信息，我们将采取合理的技术和管理措施来保护您的个人信息安全，防止未经授权的访问、使用、披露、修改或损坏\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"subtitle\">三、隐私权的行使</view>\r\n\t\t\t<view class=\"content-item\">\r\n\t\t\t\t1. 您可以随时通过微信小程序内的设置或联系我们的客服电话，查询、更正、删除您的个人信息，也可以撤回您的同意。\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"foot\">\r\n\t\t\t<view >更新时间：2025年1月1日</view>\r\n\t\t\t<view >生效时间：2025年1月1日</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.fui-wrap{\r\n\t\tpadding: 28rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 40rpx;\r\n\t}\r\n\t.subtitle{\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 30rpx;\r\n\t\tpadding: 20rpx 0;\r\n\t\t\r\n\t}\r\n\t.content-item{\r\n\t\tpadding-left: 34rpx;\r\n\t}\r\n\t.foot{\r\n\t\tpadding-top: 80rpx;\r\n\t\ttext-align: right;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tline-height: 40rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieyi.vue?vue&type=style&index=0&id=7cc02910&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieyi.vue?vue&type=style&index=0&id=7cc02910&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881718\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}