<template>
	<view class="order_detail_page">
		<view class="container">
			<view class="order_no_title">
				<view class="order_no">
					{{detail.orderNo}}
				</view>
				<view class="status">
					<u-tag v-if="detail.status == 0" text="审核中" type="info" />
					<u-tag v-if="detail.status == 1" text="待支付" type="info" />
					<u-tag v-if="detail.status == 2" text="待发货" type="primary" />
					<u-tag v-if="detail.status == 4" text="已完成" type="success" />
					<u-tag v-if="detail.status == 5" text="已取消" type="error" />
					<u-tag v-if="detail.status == 3" text="待收货" type="warning" />
				</view>
			</view>
			<view class="container_item">
				<view class="title">
					材料类型：
				</view>
				<view class="desc">
					{{detail.type}}
				</view>
			</view>
			<view class="container_item">
				<view class="title">
					板子大小：
				</view>
				<view class="desc">
					{{detail.pcbWidth}}*{{detail.pcbHeight}}mm
				</view>
			</view>
			<view class="container_item">
				<view class="title">
					板子数量：
				</view>
				<view class="desc">
					{{detail.num}}
				</view>
			</view>
			<view class="container_item">
				<view class="title">
					拼版款数：
				</view>
				<view class="desc">
					{{detail.styleCount1}}X{{detail.styleCount2}}
				</view>
			</view>
			<view class="container_item">
				<view class="title">
					总面积：
				</view>
				<view class="desc">
					{{detail.area}}㎡
				</view>
			</view>
			<view class="container_item">
				<view class="title">
					开票方式：
				</view>
				<view class="desc">
					{{detail.billType}}
				</view>
			</view>
			<view class="container_item">
				<view class="title">
					总价：
				</view>
				<view class="desc">
					￥{{detail.totalPrice}}
				</view>
			</view>
			<view class="container_item" v-if="detail.status == 4">
				<view class="title">
					申请钢网资料：
				</view>
				<view class="desc">
					<u-button v-if="detail.applicationFile == 0" type="primary" text="申请" @click="shenqing"></u-button>
					<u-tag v-if="detail.applicationFile == 1" text="已申请" type="warning" />
					<u-button v-if="detail.applicationFile == 2" type="primary" text="浏览器下载" @click="xiazai"></u-button>
				</view>
			</view>
			  <u-collapse>
			    <u-collapse-item  title="详细参数" name="Docs guide">
					<view class="collapse_item">
						<view class="title">
							厚度：
						</view>
						<view class="desc">
							{{detail.thickness}}
						</view>
					</view>
					<view class="collapse_item">
						<view class="title">
							铜箔厚度：
						</view>
						<view class="desc">
							{{detail.cuThickness}}
						</view>
					</view>
					<view class="collapse_item">
						<view class="title">
							层数：
						</view>
						<view class="desc">
							{{detail.layer}}
						</view>
					</view>
					<view class="collapse_item">
						<view class="title">
							阻焊颜色：
						</view>
						<view class="desc">
							{{detail.weldColor}}
						</view>
					</view>
					<view class="collapse_item">
						<view class="title">
							字符颜色：
						</view>
						<view class="desc">
							{{detail.charColor}}
						</view>
					</view>
					<view class="collapse_item">
						<view class="title">
							阻焊覆盖：
						</view>
						<view class="desc">
							{{detail.cover}}
						</view>
					</view>
					<view class="collapse_item">
						<view class="title">
							焊盘喷锡：
						</view>
						<view class="desc">
							{{detail.surface}}
						</view>
					</view>
					<view class="collapse_item" v-if="detail.thermalConductivity !== '不显示'">
						<view class="title">
							导热系数：
						</view>
						<view class="desc">
							{{detail.thermalConductivity}}
						</view>
					</view>
					<view class="collapse_item">
						<view class="title">
							测试方式：
						</view>
						<view class="desc">
							{{detail.test}}
						</view>
					</view>
					<view class="collapse_item">
						<view class="title">
							成型：
						</view>
						<view class="desc">
							{{detail.molding}}
						</view>
					</view>
					<view class="collapse_item">
						<view class="title">
							出货方式：
						</view>
						<view class="desc">
							{{detail.outWay}}
						</view>
					</view>
			    </u-collapse-item>
			  </u-collapse>
		</view>
	</view>
</template>

<script>
	import { baseUrl } from "../../../config"
	import { getOrder, updateOrder } from "@/api/pcb/order.js"
	export default {
		data() {
			return {
				detail:{},
				baseUrl,
			}
		},
		onLoad({id}) {
			this.getDetail(id)
		},
		methods: {
			getDetail(id){
				getOrder(id).then(res=>{
					this.detail = res.data
					console.log(this.detail);
				})
			},
			shenqing(){
				this.detail.applicationFile = 1
				updateOrder(this.detail).then(res=>{
					this.getDetail(this.detail.id)
				})
			},
			xiazai(){
				// 构建完整的下载链接，去掉/prod-api部分
				let downloadUrl = this.baseUrl + this.detail.informationFile;
				downloadUrl = downloadUrl.replace('/prod-api', '');

				// #ifdef H5
				// H5端直接在新窗口打开下载链接
				window.open(downloadUrl, '_blank');
				// #endif

				// #ifdef MP-WEIXIN
				// 微信小程序端复制链接到剪贴板，提示用户在浏览器中打开
				uni.setClipboardData({
					data: downloadUrl,
					success: () => {
						uni.showModal({
							title: '下载提示',
							content: '下载链接已复制到剪贴板，请在浏览器中粘贴打开进行下载',
							showCancel: false,
							confirmText: '知道了'
						});
					},
					fail: () => {
						uni.showToast({
							icon: 'none',
							title: '复制失败，请重试'
						});
					}
				});
				// #endif

				// #ifdef APP-PLUS
				// App端使用系统浏览器打开下载链接
				plus.runtime.openURL(downloadUrl);
				// #endif
			}
		}
	}
</script>

<style lang="scss">
	.order_detail_page{
		padding-top: 30rpx;
		.container{
			width: 90vw;
			border-radius: 12rpx;
			background-color: #fff;
			margin: 0 auto;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
			padding: 20rpx;
			.order_no_title{
				display: flex;
				justify-content: space-between;
				padding: 0 30rpx;
				.order_no{
					font-weight: bold;
				}
			}
			.container_item{
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 30rpx;
				padding-bottom: 20rpx;
				border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
				.title{
					font-weight: bold;
				}
				.desc{
					
				}
			}
		}
	}
	.collapse_item{
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
		.title{
			font-weight: bold;
		}
		.desc{
			
		}
	}
</style>
