{"version": 3, "sources": ["webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue?b624", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue?0b64", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue?4d3f", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue?11dc", "uni-app:///uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue?e94d", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue?b85b"], "names": ["name", "mixins", "data", "elId", "animationData", "expanded", "showBorder", "animating", "parentData", "accordion", "border", "watch", "clearTimeout", "mounted", "methods", "init", "value", "children", "updateParentData", "setContentAnimate", "rect", "height", "animation", "timingFunction", "step", "duration", "uni", "clickHandler", "queryRect", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAwqB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8C5rB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,eAiBA;EACAA;EACAC;EACAC;IACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAN;MAAA;MACAO;MACA;MACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;MACA,mBAIA;QAHAC;QACAP;QAAA,qCACAQ;QAAAA;MAGA;QACA;UACA;QACA;QACA;MACA;QACA;UACA;QACA;QACA;UAAA;QAAA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACAC;gBACA;gBAiBAC;kBACAC;gBACA;gBACAD,UACAD,eACAG;kBACAC;gBACA,GACAD;gBACA;gBACA;gBACA;gBACAE;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MAEA;MACA;MACA;QACA;UACAC;QACA;MACA;IAYA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5MA;AAAA;AAAA;AAAA;AAA2xC,CAAgB,goCAAG,EAAC,C;;;;;;;;;;;ACA/yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-collapse-item.vue?vue&type=template&id=2675fb21&scoped=true&\"\nvar renderjs\nimport script from \"./u-collapse-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-collapse-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-collapse-item.vue?vue&type=style&index=0&id=2675fb21&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2675fb21\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse-item.vue?vue&type=template&id=2675fb21&scoped=true&\"", "var components\ntry {\n  components = {\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n    uLine: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-line/u-line\" */ \"@/uni_modules/uview-ui/components/u-line/u-line.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse-item.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-collapse-item\">\r\n\t\t<u-cell\r\n\t\t\t:title=\"title\"\r\n\t\t\t:value=\"value\"\r\n\t\t\t:label=\"label\"\r\n\t\t\t:icon=\"icon\"\r\n\t\t\t:isLink=\"isLink\"\r\n\t\t\t:clickable=\"clickable\"\r\n\t\t\t:border=\"parentData.border && showBorder\"\r\n\t\t\t@click=\"clickHandler\"\r\n\t\t\t:arrowDirection=\"expanded ? 'up' : 'down'\"\r\n\t\t\t:disabled=\"disabled\"\r\n\t\t>\r\n\t\t\t<!-- #ifndef MP-WEIXIN -->\r\n\t\t\t<!-- 微信小程序不支持，因为微信中不支持 <slot name=\"title\" slot=\"title\" />的写法 -->\r\n\t\t\t<template slot=\"title\">\n\t\t\t\t<slot name=\"title\"></slot>\n\t\t\t</template>\n\t\t\t<template slot=\"icon\">\n\t\t\t\t<slot name=\"icon\"></slot>\n\t\t\t</template>\n\t\t\t<template slot=\"value\">\n\t\t\t\t<slot name=\"value\"></slot>\n\t\t\t</template>\n\t\t\t<template slot=\"right-icon\">\n\t\t\t\t<slot name=\"right-icon\"></slot>\n\t\t\t</template>\r\n\t\t\t<!-- #endif -->\r\n\t\t</u-cell>\r\n\t\t<view\r\n\t\t\tclass=\"u-collapse-item__content\"\r\n\t\t\t:animation=\"animationData\"\r\n\t\t\tref=\"animation\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-collapse-item__content__text content-class\"\r\n\t\t\t\t:id=\"elId\"\r\n\t\t\t\t:ref=\"elId\"\r\n\t\t\t><slot /></view>\r\n\t\t</view>\r\n\t\t<u-line v-if=\"parentData.border\"></u-line>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t// #ifdef APP-NVUE\r\n\tconst animation = uni.requireNativePlugin('animation')\r\n\tconst dom = uni.requireNativePlugin('dom')\r\n\t// #endif\r\n\t/**\r\n\t * collapseItem 折叠面板Item\r\n\t * @description 通过折叠面板收纳内容区域（搭配u-collapse使用）\r\n\t * @tutorial https://www.uviewui.com/components/collapse.html\r\n\t * @property {String}\t\t\ttitle \t\t标题\r\n\t * @property {String}\t\t\tvalue \t\t标题右侧内容\r\n\t * @property {String}\t\t\tlabel \t\t标题下方的描述信息\r\n\t * @property {Boolean}\t\t\tdisbled \t是否禁用折叠面板 ( 默认 false )\r\n\t * @property {Boolean}\t\t\tisLink \t\t是否展示右侧箭头并开启点击反馈 ( 默认 true )\r\n\t * @property {Boolean}\t\t\tclickable\t是否开启点击反馈 ( 默认 true )\r\n\t * @property {Boolean}\t\t\tborder\t\t是否显示内边框 ( 默认 true )\r\n\t * @property {String}\t\t\talign\t\t标题的对齐方式 ( 默认 'left' )\r\n\t * @property {String | Number}\tname\t\t唯一标识符\r\n\t * @property {String}\t\t\ticon\t\t标题左侧图片，可为绝对路径的图片或内置图标\r\n\t * @event {Function}\t\t\tchange \t\t\t某个item被打开或者收起时触发\r\n\t * @example <u-collapse-item :title=\"item.head\" v-for=\"(item, index) in itemList\" :key=\"index\">{{item.body}}</u-collapse-item>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-collapse-item\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\telId: uni.$u.guid(),\r\n\t\t\t\t// uni.createAnimation的导出数据\r\n\t\t\t\tanimationData: {},\r\n\t\t\t\t// 是否展开状态\r\n\t\t\t\texpanded: false,\r\n\t\t\t\t// 根据expanded确定是否显示border，为了控制展开时，cell的下划线更好的显示效果，进行一定时间的延时\r\n\t\t\t\tshowBorder: false,\r\n\t\t\t\t// 是否动画中，如果是则不允许继续触发点击\r\n\t\t\t\tanimating: false,\r\n\t\t\t\t// 父组件u-collapse的参数\r\n\t\t\t\tparentData: {\r\n\t\t\t\t\taccordion: false,\r\n\t\t\t\t\tborder: false\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\texpanded(n) {\r\n\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\tthis.timer = null\r\n\t\t\t\t// 这里根据expanded的值来进行一定的延时，是为了cell的下划线更好的显示效果\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.showBorder = n\r\n\t\t\t\t}, n ? 10 : 290)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 异步获取内容，或者动态修改了内容时，需要重新初始化\r\n\t\t\tinit() {\r\n\t\t\t\t// 初始化数据\r\n\t\t\t\tthis.updateParentData()\r\n\t\t\t\tif (!this.parent) {\r\n\t\t\t\t\treturn uni.$u.error('u-collapse-item必须要搭配u-collapse组件使用')\r\n\t\t\t\t}\r\n\t\t\t\tconst {\r\n\t\t\t\t\tvalue,\r\n\t\t\t\t\taccordion,\r\n\t\t\t\t\tchildren = []\r\n\t\t\t\t} = this.parent\r\n\r\n\t\t\t\tif (accordion) {\r\n\t\t\t\t\tif (uni.$u.test.array(value)) {\r\n\t\t\t\t\t\treturn uni.$u.error('手风琴模式下，u-collapse组件的value参数不能为数组')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.expanded = this.name == value\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (!uni.$u.test.array(value) && value !== null) {\r\n\t\t\t\t\t\treturn uni.$u.error('非手风琴模式下，u-collapse组件的value参数必须为数组')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.expanded = (value || []).some(item => item == this.name)\r\n\t\t\t\t}\r\n\t\t\t\t// 设置组件的展开或收起状态\r\n\t\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t\tthis.setContentAnimate()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tupdateParentData() {\r\n\t\t\t\t// 此方法在mixin中\r\n\t\t\t\tthis.getParentData('u-collapse')\r\n\t\t\t},\r\n\t\t\tasync setContentAnimate() {\r\n\t\t\t\t// 每次面板打开或者收起时，都查询元素尺寸\r\n\t\t\t\t// 好处是，父组件从服务端获取内容后，变更折叠面板后可以获得最新的高度\r\n\t\t\t\tconst rect = await this.queryRect()\r\n\t\t\t\tconst height = this.expanded ? rect.height : 0\r\n\t\t\t\tthis.animating = true\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tconst ref = this.$refs['animation'].ref\r\n\t\t\t\tanimation.transition(ref, {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\theight: height + 'px'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tduration: this.duration,\r\n\t\t\t\t\t// 必须设置为true，否则会到面板收起或展开时，页面其他元素不会随之调整它们的布局\r\n\t\t\t\t\tneedLayout: true,\r\n\t\t\t\t\ttimingFunction: 'ease-in-out',\r\n\t\t\t\t}, () => {\r\n\t\t\t\t\tthis.animating = false\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tconst animation = uni.createAnimation({\r\n\t\t\t\t\ttimingFunction: 'ease-in-out',\r\n\t\t\t\t});\r\n\t\t\t\tanimation\r\n\t\t\t\t\t.height(height)\r\n\t\t\t\t\t.step({\r\n\t\t\t\t\t\tduration: this.duration,\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.step()\r\n\t\t\t\t// 导出动画数据给面板的animationData值\r\n\t\t\t\tthis.animationData = animation.export()\r\n\t\t\t\t// 标识动画结束\r\n\t\t\t\tuni.$u.sleep(this.duration).then(() => {\r\n\t\t\t\t\tthis.animating = false\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 点击collapsehead头部\r\n\t\t\tclickHandler() {\r\n\t\t\t\tif (this.disabled && this.animating) return\r\n\t\t\t\t// 设置本组件为相反的状态\r\n\t\t\t\tthis.parent && this.parent.onChange(this)\r\n\t\t\t},\r\n\t\t\t// 查询内容高度\r\n\t\t\tqueryRect() {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t// $uGetRect为uView自带的节点查询简化方法，详见文档介绍：https://www.uviewui.com/js/getRect.html\r\n\t\t\t\t// 组件内部一般用this.$uGetRect，对外的为uni.$u.getRect，二者功能一致，名称不同\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tthis.$uGetRect(`#${this.elId}`).then(size => {\r\n\t\t\t\t\t\tresolve(size)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// nvue下，使用dom模块查询元素高度\r\n\t\t\t\t// 返回一个promise，让调用此方法的主体能使用then回调\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tdom.getComponentRect(this.$refs[this.elId], res => {\r\n\t\t\t\t\t\tresolve(res.size)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-collapse-item {\r\n\r\n\t\t&__content {\r\n\t\t\toverflow: hidden;\r\n\t\t\theight: 0;\r\n\r\n\t\t\t&__text {\r\n\t\t\t\tpadding: 12px 15px;\r\n\t\t\t\tcolor: $u-content-color;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tline-height: 18px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse-item.vue?vue&type=style&index=0&id=2675fb21&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse-item.vue?vue&type=style&index=0&id=2675fb21&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541882451\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}