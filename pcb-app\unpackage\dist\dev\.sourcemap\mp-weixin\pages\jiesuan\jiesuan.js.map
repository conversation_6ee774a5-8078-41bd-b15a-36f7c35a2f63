{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/jiesuan/jiesuan.vue?4f54", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/jiesuan/jiesuan.vue?19f9", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/jiesuan/jiesuan.vue?68cc", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/jiesuan/jiesuan.vue?9b53", "uni-app:///pages/jiesuan/jiesuan.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/jiesuan/jiesuan.vue?7676", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/jiesuan/jiesuan.vue?2f1d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "params", "transitionShow", "priceData", "onLoad", "methods", "changeShow", "addCar", "uni", "url", "title", "content", "showCancel", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC0ItpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;UACAC;YACAC;UACA;QACA;UACAD;YACAE;YACAC;YACAC;YACAC;cACAL;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxMA;AAAA;AAAA;AAAA;AAAqsC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACAztC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/jiesuan/jiesuan.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/jiesuan/jiesuan.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./jiesuan.vue?vue&type=template&id=16f35f32&\"\nvar renderjs\nimport script from \"./jiesuan.vue?vue&type=script&lang=js&\"\nexport * from \"./jiesuan.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jiesuan.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/jiesuan/jiesuan.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jiesuan.vue?vue&type=template&id=16f35f32&\"", "var components\ntry {\n  components = {\n    uTransition: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-transition/u-transition\" */ \"@/uni_modules/uview-ui/components/u-transition/u-transition.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jiesuan.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jiesuan.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"topBack\"></view>\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<text>板材类型</text>\r\n\t\t\t\t<text>数量</text>\r\n\t\t\t\t<text>总价(元)</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<text>{{params.type}}</text>\r\n\t\t\t\t<text>{{params.num}}</text>\r\n\t\t\t\t<text>{{priceData.totalPrice}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"bottomBtn\">\r\n\t\t\t<u-transition :show=\"transitionShow\">\r\n\t\t\t    <view class=\"detail\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view style=\"font-size: 28rpx;font-weight: bold;width: 100vw;text-align: center;\">\r\n\t\t\t\t\t\t\t参数详情\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<scroll-view scroll-y=\"true\" style=\"height: 300rpx;\" :show-scrollbar=\"true\" >\r\n\t\t\t\t\t\t\t<view style=\"padding: 0 30rpx;\">\r\n\t\t\t\t\t\t\t\t<view style=\"border-bottom: 1rpx solid darkgray;\">\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-weight: bold;\">\r\n\t\t\t\t\t\t\t\t\t\t基本信息\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view style=\"display: flex;flex-direction: column;gap: 20rpx;margin-top: 20rpx;padding-bottom: 30rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">板材类别:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.type}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">板子层数:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.layer}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">产品面积:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.area}}㎡</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">拼版款数:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.styleCount1}}X{{params.styleCount2}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">板子大小:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.pcbWidth}}mm * {{params.pcbHeight}}mm</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">板子数量:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.num}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">出货方式:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.outWay}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">开票方式:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.billType}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">订单交期:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.deliveryDate}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view style=\"border-bottom: 1rpx solid darkgray;margin-top: 20rpx;\">\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-weight: bold;\">\r\n\t\t\t\t\t\t\t\t\t\t工艺信息\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view style=\"display: flex;flex-direction: column;gap: 20rpx;margin-top: 20rpx;padding-bottom: 30rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">板子厚度:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.thickness}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">铜箔厚度:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.cuThickness}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">阻焊颜色:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.weldColor}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">字符颜色:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.charColor}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">阻焊覆盖:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.cover}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">焊盘喷锡:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.surface}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">测试方式:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.test}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">成型方式:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.molding}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view style=\"display: flex;gap: 150rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 24rpx;color: darkgray;\">导热系数:</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text style=\"font-size: 26rpx;\">{{params.thermalConductivity}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"background-color: #fff;padding: 10rpx;width: 90vw;margin: 20rpx auto;border-radius: 6rpx;height: 200rpx;margin-top: 40rpx;\">\r\n\t\t\t\t\t\t<text style=\"font-weight: bold;font-size: 28rpx;margin-left: 20rpx;\">PCB订单备注</text>\r\n\t\t\t\t\t\t<textarea cols=\"38\" rows=\"2\" v-model=\"params.remark\" class=\"input\" placeholder=\"有特殊要求,请填写此处\"  />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</u-transition>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t预估总价(不含税运):\r\n\t\t\t\t\t\t<text style=\"color: rgb(255,153,0);font-weight: bold;font-size: 26rpx;\">￥{{priceData.totalPrice}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"color: darkgray;\" @click=\"changeShow\">\r\n\t\t\t\t\t\t明细/详情\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t<u-button type=\"warning\" :text=\"params.isBack == 1 ? '下单' : '加入购物车'\" @click=\"addCar\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { addOrder,getPrice } from \"@/api/pcb/order.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tparams:{},\r\n\t\t\t\ttransitionShow:false,\r\n\t\t\t\tpriceData:{}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tlet pages = getCurrentPages()\r\n\t\t\tlet page = pages[pages.length - 2]\r\n\t\t\tlet params = page.data.params;\r\n\t\t\tthis.params = params\r\n\t\t\tthis.params.userId = this.$store.state.user.userId\r\n\t\t\tthis.params.prepayId = null\r\n\t\t\tthis.params.status = 0;\r\n\r\n\t\t\tif (this.params.isBack == 1) {\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\tconst year = now.getFullYear();\r\n\t\t\t\tconst month = (now.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\t\tconst day = now.getDate().toString().padStart(2, '0');\r\n\t\t\t\tconst hours = now.getHours().toString().padStart(2, '0');\r\n\t\t\t\tconst minutes = now.getMinutes().toString().padStart(2, '0');\r\n\t\t\t\tconst seconds = now.getSeconds().toString().padStart(2, '0');\r\n\t\t\t\tthis.params.orderTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tgetPrice(this.params).then(res=>{\r\n\t\t\t\tthis.priceData = res.data\r\n\t\t\t\tthis.params.totalPrice = res.data.totalPrice\r\n\t\t\t\tthis.params.onlinePrice = res.data.onlinePrice\r\n\t\t\t\tthis.params.offlinePrice = res.data.offlinePrice\r\n\t\t\t\tthis.params.area = res.data.area\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tchangeShow(){\r\n\t\t\t\tthis.transitionShow = !this.transitionShow\r\n\t\t\t},\r\n\t\t\taddCar(){\r\n\t\t\t\taddOrder(this.params).then(res=>{\r\n\t\t\t\t\tif(this.params.isBack == 2){\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl:\"/pages/car/car\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle:\"成功\",\r\n\t\t\t\t\t\t\tcontent:\"下单成功，等待审核\",\r\n\t\t\t\t\t\t\tshowCancel:false,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl:\"/pages/index\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.page{\r\n\t\toverflow: hidden;\r\n\t\theight: 100vh;\r\n\t\t.topBack{\r\n\t\t\tbackground-color: rgb(147,116,84);\r\n\t\t\twidth: 100vw;\r\n\t\t\theight: 150rpx;\r\n\t\t\tposition: absolute;\r\n\t\t\tz-index: -999;\r\n\t\t\tborder-radius: 100% / 0 0 50% 50%;\r\n\t\t}\r\n\t\t.container{\r\n\t\t\twidth: 95vw;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tmargin: 30rpx auto;\r\n\t\t\tpadding-top: 15rpx;\r\n\t\t\tpadding-bottom: 200rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\t.title{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tbackground-color: rgb(247,245,242);\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tgap: 150rpx;\r\n\t\t\t\tpadding: 0 80rpx;\r\n\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t}\r\n\t\t\t.content{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tgap: 150rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tpadding: 0 80rpx;\r\n\t\t\t\tborder: 1rpx solid rgba(0,0,0,0.1);\r\n\t\t\t\twidth: 95%;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.bottomBtn{\r\n\t\t\twidth: 100vw;\r\n\t\t\tposition: fixed;\r\n\t\t\tz-index: 999;\r\n\t\t\tbottom: 0;\r\n\t\t\t.detail{\r\n\t\t\t\twidth: 100vw;\r\n\t\t\t\theight: 600rpx;\r\n\t\t\t\tbackground-color: rgb(247,245,242);\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 30rpx 0;\r\n\t\t\t\t.input{\r\n\t\t\t\t\tborder: 1rpx solid rgba(0, 0, 0, 0.06);\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\twidth: 95%;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tmargin: 10rpx auto;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.content{\r\n\t\t\t\tbackground-color: rgb(249,249,250);\r\n\t\t\t\tpadding: 20rpx 20rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tgap: 10rpx;\r\n\t\t\t\tbox-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t\t\t.desc{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jiesuan.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jiesuan.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881867\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}