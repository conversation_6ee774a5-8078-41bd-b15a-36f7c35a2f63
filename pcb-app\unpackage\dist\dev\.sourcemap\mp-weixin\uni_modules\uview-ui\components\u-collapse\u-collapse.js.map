{"version": 3, "sources": ["webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse/u-collapse.vue?e2e1", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse/u-collapse.vue?e4bf", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse/u-collapse.vue?7873", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse/u-collapse.vue?0181", "uni-app:///uni_modules/uview-ui/components/u-collapse/u-collapse.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse/u-collapse.vue?8b42", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/uni_modules/uview-ui/components/u-collapse/u-collapse.vue?6990"], "names": ["name", "mixins", "watch", "needInit", "created", "computed", "parentData", "init", "child", "onChange", "changeArr", "status"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACQvrB;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;EAWAA;EACAC;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAF;MACA;MACA;MACA;IACA;EACA;AAAA,iEACA;EACA;EACAG;IACA;MACA;QACA;QACA;MACA;IACA;EACA;AACA,oEACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACA;AACA;AACA;AACA;EACAC;IAAA;IACA;IACA;MACA;MACA;QACAD;QACAA;MACA;QACA;UACAA;UACAA;QACA;MACA;MACA;MACAE;QACA;QACAV;QACAW;MACA;IACA;IAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAAsxC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACA1yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-collapse/u-collapse.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-collapse.vue?vue&type=template&id=52cf776e&scoped=true&\"\nvar renderjs\nimport script from \"./u-collapse.vue?vue&type=script&lang=js&\"\nexport * from \"./u-collapse.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-collapse.vue?vue&type=style&index=0&id=52cf776e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"52cf776e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-collapse/u-collapse.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse.vue?vue&type=template&id=52cf776e&scoped=true&\"", "var components\ntry {\n  components = {\n    uLine: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-line/u-line\" */ \"@/uni_modules/uview-ui/components/u-line/u-line.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-collapse\">\n\t\t<u-line v-if=\"border\"></u-line>\n\t\t<slot />\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * collapse 折叠面板 \n\t * @description 通过折叠面板收纳内容区域\n\t * @tutorial https://www.uviewui.com/components/collapse.html\n\t * @property {String | Number | Array}\tvalue\t\t当前展开面板的name，非手风琴模式：[<string | number>]，手风琴模式：string | number\n\t * @property {Boolean}\t\t\t\t\taccordion\t是否手风琴模式（ 默认 false ）\n\t * @property {Boolean}\t\t\t\t\tborder\t\t是否显示外边框 ( 默认 true ）\n\t * @event {Function}\tchange \t\t当前激活面板展开时触发(如果是手风琴模式，参数activeNames类型为String，否则为Array)\n\t * @example <u-collapse></u-collapse>\n\t */\n\texport default {\n\t\tname: \"u-collapse\",\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\twatch: {\n\t\t\tneedInit() {\n\t\t\t\tthis.init()\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.children = []\n\t\t},\n\t\tcomputed: {\n\t\t\tneedInit() {\n\t\t\t\t// 通过computed，同时监听accordion和value值的变化\n\t\t\t\t// 再通过watch去执行init()方法，进行再一次的初始化\n\t\t\t\treturn [this.accordion, this.value]\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 当父组件需要子组件需要共享的参数发生了变化，手动通知子组件\n\t\t\tparentData() {\n\t\t\t\tif (this.children.length) {\n\t\t\t\t\tthis.children.map(child => {\n\t\t\t\t\t\t// 判断子组件(u-checkbox)如果有updateParentData方法的话，就就执行(执行的结果是子组件重新从父组件拉取了最新的值)\n\t\t\t\t\t\ttypeof(child.updateParentData) === 'function' && child.updateParentData()\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tmethods: {\n\t\t\t// 重新初始化一次内部的所有子元素\n\t\t\tinit() {\n\t\t\t\tthis.children.map(child => {\n\t\t\t\t\tchild.init()\n\t\t\t\t})\n\t\t\t},\n\t\t\t/**\n\t\t\t * collapse-item被点击时触发，由collapse统一处理各子组件的状态\n\t\t\t * @param {Object} target 被操作的面板的实例\n\t\t\t */\n\t\t\tonChange(target) {\n\t\t\t\tlet changeArr = []\n\t\t\t\tthis.children.map((child, index) => {\n\t\t\t\t\t// 如果是手风琴模式，将其他的折叠面板收起来\n\t\t\t\t\tif (this.accordion) {\n\t\t\t\t\t\tchild.expanded = child === target ? !target.expanded : false\n\t\t\t\t\t\tchild.setContentAnimate()\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif(child === target) {\n\t\t\t\t\t\t\tchild.expanded = !child.expanded\n\t\t\t\t\t\t\tchild.setContentAnimate()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t// 拼接change事件中，数组元素的状态\n\t\t\t\t\tchangeArr.push({\n\t\t\t\t\t\t// 如果没有定义name属性，则默认返回组件的index索引\n\t\t\t\t\t\tname: child.name || index,\n\t\t\t\t\t\tstatus: child.expanded ? 'open' : 'close'\n\t\t\t\t\t})\n\t\t\t\t})\n\n\t\t\t\tthis.$emit('change', changeArr)\n\t\t\t\tthis.$emit(target.expanded ? 'open' : 'close', target.name)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n</style>\n", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse.vue?vue&type=style&index=0&id=52cf776e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-collapse.vue?vue&type=style&index=0&id=52cf776e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541882439\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}