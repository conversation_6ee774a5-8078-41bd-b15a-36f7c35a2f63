<template>
	<view class="page">
		<u-swiper :list="swiperList" indicator style="width: 95vw;margin: 20rpx auto;border-radius: 10rpx;"
		   height="400rpx"/> 
		<!-- <u-notice-bar class="noti" :text="notiData" direction="column" :disableTouch="true"/> -->
		<view class="bar2">
			<view class="item">
				<image src="../static/images/home/<USER>"/>
				<text class="title">A级板材</text>
				<!-- <text class="desc">建滔/生益</text> -->
			</view>
			<view class="item">
				<image src="../static/images/home/<USER>"/>
				<text class="title">极速出货</text>
				<!-- <text class="desc">24小时出货</text> -->
			</view>
			<view class="item">
				<button open-type="contact" class="contact-button">
					<image src="../static/images/home/<USER>"/>
					<text class="title">专业客服</text>
					<!-- <text class="desc">24小时在线</text> -->
				</button>
			</view>
		</view>
		<view class="bar3">
			<view class="item" @click="buliding">
				<view class="imageBg">
					<image src="../static/images/home/<USER>" />
				</view>
				<view class="text">
					<text class="title">账号归属</text>
					<text class="dsc">完成设置享更多福利</text>
				</view>
			</view>
			<view class="item" @click="toGoodsCenter">
				<view class="imageBg">
					<image src="../static/images/home/<USER>" />
				</view>
				<view class="text">
					<text class="title">产品中心</text>
					<text class="dsc">产品质量有保障</text>
				</view>
			</view>
		</view>
		<view class="bar3">
			<view class="item" @click="buliding">
				<view class="imageBg">
					<image src="../static/images/home/<USER>" />
				</view>
				<view class="text">
					<text class="title">积分商城</text>
					<text class="dsc">海量好礼免费换</text>
				</view>
			</view>
			<view class="item">
				<button open-type="contact" class="contact-button">
					<view class="imageBg">
						<image src="../static/images/home/<USER>" />
					</view>
					<view class="text">
						<text class="title">在线客服</text>
						<text class="dsc">专业客服在线答疑</text>
					</view>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { listSwiper } from "@/api/pcb/swiper.js"
	import { baseUrl } from "@/config.js"
  export default {
    data() {
      return {
		  swiperList:[],
		  baseUrl,
		  notiData:['通知1','通知2']
      }
    },
	onLoad() {
		this.getSwiperList()
	},
    methods: {
		getSwiperList(){
			listSwiper({pageSize:10000}).then(res=>{
				res.rows.forEach(item => {
					this.swiperList.push(this.baseUrl+item.pic)
				})
			})
		},
		toGoodsCenter(){
			uni.navigateTo({
				url:"/pages/goodsCenter/goodsCenter"
			})
		},
		buliding(){
			// uni.showToast({
			// 	title:"模块正在建设……",
			// 	icon:"none"
			// })
		}
    }
  }
</script>

<style lang="scss">
	.page{
		background-color: rgb(246,246,246);
		min-height: 100vh;
		.noti{
			width: 95vw;
			margin: 10rpx auto;
		}
		.bar2{
			background-color: #fff;
			border-radius: 15rpx;
			display: flex;
			justify-content: space-between;
			padding: 20rpx 50rpx;
			width: 95vw;
			margin: 20rpx auto;
			box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
			.item{
				display: flex;
				flex-direction: column;
				align-items: center;
				.contact-button {
					margin: 0;
					padding: 0;
					border: none;
					background-color: transparent;
					line-height: normal;
					text-align: center;
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 100%;
					height: 100%;
					position: relative;
					border-radius: 0;
					outline: none;
					&::after {
						border: none;
					}
				}
				image{
					width: 50rpx;
					height: 50rpx;
				}
				.title{
					font-size: 30rpx;
					font-weight: bold;
				}
				.desc{
					font-size: 20rpx;
					color: darkgray;
				}
			}
		}
		.bar3{
			width: 95vw;
			margin: 20rpx auto;
			display: flex;
			justify-content: space-between;
			.item{
				background-color: #fff;
				padding: 30rpx 10rpx 30rpx 20rpx;
				width: 49%;
				border-radius: 12rpx;
				box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
				display: flex;
				align-items: center;
				.contact-button {
					margin: 0;
					padding: 0;
					border: none;
					background-color: transparent;
					line-height: normal;
					text-align: left;
					display: flex;
					align-items: center;
					width: 100%;
					height: 100%;
					position: relative;
					border-radius: 0;
					outline: none;
					&::after {
						border: none;
					}
				}
				.imageBg{
					background-color: rgb(255,245,229);
					border-radius: 50%;
					width: 70rpx;
					height: 70rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-right: 20rpx;
					image{
						width: 40rpx;
						height: 40rpx;
					}
				}
				.text{
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					width: 220rpx;
					height: 80rpx;
					.title{
						font-weight: bold;
						font-size: 30rpx;
					}
					.dsc{
						font-size: 22rpx;
					}
				}
			}
		}
	}
</style>
