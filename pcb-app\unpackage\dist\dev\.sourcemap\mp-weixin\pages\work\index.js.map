{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/work/index.vue?f7ed", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/work/index.vue?5766", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/work/index.vue?228b", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/work/index.vue?b44f", "uni-app:///pages/work/index.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/work/index.vue?1c14", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/work/index.vue?50c1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "collapseValue", "chooseNameShow", "baseUrl", "params", "layer", "type", "typeId", "num", "thickness", "cuThickness", "test", "charColor", "weldColor", "cover", "molding", "surface", "outWay", "pcb<PERSON>id<PERSON>", "pcbHeight", "area", "price", "deliveryDate", "billType", "thermalConductivity", "styleCount1", "styleCount2", "modelName", "isBack", "typeList", "choosedType", "choosePopupShow", "allCheckPopupShow", "popupTitle", "popupData", "chooseData", "<PERSON><PERSON><PERSON><PERSON>", "padding", "popupHeight", "priceModel", "layerList", "choosePriceModel", "isPCS", "onLoad", "pageSize", "methods", "nameChoose", "chong<PERSON>", "params<PERSON><PERSON>", "chooseNamePopupClose", "getGoodsList", "chooseType", "chooseNameConfirm", "choosePopupClose", "choosePopupConfirm", "uni", "title", "icon", "allCheckPopupClose", "layerChoose", "text", "choosePopup", "allChoosePopup", "tempArr", "numInput", "numChoose", "thicknessChoose", "cuThicknessChoose", "deliveryDateChoose", "testChoose", "outChoose", "charColorChoose", "weldColorChoose", "coverChoose", "moldingChoose", "surfaceChoose", "billType<PERSON><PERSON>ose", "thermalConductivityChoose", "initPopupData", "arr", "next", "condition", "message", "url", "changeChoosePriceModel", "totalArea", "console", "typeItemId", "pcbWidthInput", "pcbHeightInput", "styleCount1Input", "styleCount2Input", "setParams"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACwUppB;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACA9B;QACAG;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAK;QACAC;QACAC;MACA;MACAY;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;MAAAC;IAAA;MACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QAAAN;MAAA;QACA;MACA;IACA;IACAO;MAAA;MACA;MACA;QAAAP;QAAArC;MAAA;QACA;QACA;UAAA;QAAA;MACA;IACA;IACA6C;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;MACA;QACA;UAAA;QAAA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACAC;YACAC;YACAC;UACA;UACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MAAA,2CACA;QAAA;MAAA;QAAA;UAAA;UACA;YACAC;UACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACAX;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QACAhD;MACA;MACA;IACA;IACA0D;MACA;QACAZ;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAW;MACA;QACAb;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAY;MACA;QACAd;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAa;MACA;MACA;MACA;IACA;IACAC;MACA;QACAhB;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QAAA;MAAA;MACA;QACA;UACAG;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAY;MACA;QACAjB;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QAAA;MAAA;MACA;QACA;UACAG;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAa;MACA;QACAlB;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAiB;MACA;QACAnB;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAkB;MACA;QACApB;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAmB;MACA;QACArB;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAoB;MACA;QACAtB;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAqB;MAAA;MACA;MACA;MACA;MACA;MACAC;QACA;UACAnB;QACA;QACA;MACA;MACA;MACA;IACA;IACAoB;MACA,cACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACA;QAAA;QACA;UACA;UACA;QACA;MACA;MACA;QAAA;MAAA;QACA;QACA;MACA;MACA;QAAA;MAAA;QACA;QACA;MACA;MACA3B;QACA4B;MACA;IACA;IACAC;MAAA;MACA;MACA,6PACA;QACA;MACA;QACA;QACA;UACAC;QACA;QACAC;QACA;UAAA;QAAA;QACA;UACA;YACA;cAAAC;cAAAhF;cAAAF;YAAA;cACA;YACA;UACA;YACA;cAAAkF;cAAAhF;cAAAF;YAAA;cACA;YACA;UACA;QACA;MACA;IACA;IACAmF;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAN;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzvBA;AAAA;AAAA;AAAA;AAAmsC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAvtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/work/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/work/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=51b5538d&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/work/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=51b5538d&\"", "var components\ntry {\n  components = {\n    uniCollapse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-collapse/components/uni-collapse/uni-collapse\" */ \"@/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue\"\n      )\n    },\n    uniCollapseItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item\" */ \"@/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = JSON.parse(_vm.choosePriceModel.thermalConductivity)\n    .map(function (item) {\n      return item.name\n    })\n    .includes(\"不显示\")\n  var g1 = JSON.parse(_vm.choosePriceModel.molding)\n    .map(function (item) {\n      return item.name\n    })\n    .includes(\"不显示\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<page-meta :page-style=\"'overflow:'+(choosePopupShow||chooseNameShow||allCheckPopupShow?'hidden':'visible')\"></page-meta>\r\n\t<view class=\"page\">\r\n\t\t<div class=\"topBack\"></div>\r\n\t\t<uni-collapse class=\"coll\" v-model=\"collapseValue\">\r\n\t\t\t<uni-collapse-item titleBorder=\"none\" class=\"collItem\">\r\n\t\t\t\t<template v-slot:title>\r\n\t\t\t\t\t<text class=\"title\">基本信息</text>\r\n\t\t\t\t</template>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"contentItem\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">型号</text>\r\n\t\t\t\t\t\t\t<view style=\"background-color: rgb(239,239,239);padding: 10rpx;border-radius: 8rpx;\">\r\n\t\t\t\t\t\t\t\t<input v-model=\"params.modelName\" placeholder=\"请输入型号\" style=\"background-color: #fff;padding: 5rpx;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">板材类型</text>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"nameChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.type\">{{params.type}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择板材类型</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t板子层数\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"layerChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.layer\">{{params.layer}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择板子层数</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t板子大小\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"paramsInput\">\r\n\t\t\t\t\t\t\t\t<input @input=\"pcbWidthInput\" placeholder=\"长度/x\" v-model=\"params.pcbWidth\" />\r\n\t\t\t\t\t\t\t\t<view style=\"width: 80rpx;\">x</view>\r\n\t\t\t\t\t\t\t\t<input @input=\"pcbHeightInput\" placeholder=\"宽度/y\" v-model=\"params.pcbHeight\" />\r\n\t\t\t\t\t\t\t\t<view style=\"position:100rpx;\">mm</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t拼版款数\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"paramsInput\">\r\n\t\t\t\t\t\t\t\t<input type=\"number\" :disabled=\"isPCS\" @input=\"styleCount1Input\" placeholder=\"请输入\" v-model=\"params.styleCount1\" />\r\n\t\t\t\t\t\t\t\t<view style=\"width: 80rpx;\">x</view>\r\n\t\t\t\t\t\t\t\t<input type=\"number\" :disabled=\"isPCS\" @input=\"styleCount2Input\" placeholder=\"请输入\" v-model=\"params.styleCount2\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\" style=\"display: flex;justify-content: space-between;\">\r\n\t\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t\t板子数量\r\n\t\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"numChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.num\">{{params.num}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择板子数量</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t出货方式\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"outChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.outWay\">{{params.outWay}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择出货方式</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-collapse-item>\r\n\t\t\t<uni-collapse-item titleBorder=\"none\" class=\"collItem\">\r\n\t\t\t\t<template v-slot:title>\r\n\t\t\t\t\t<text class=\"title\">工艺信息</text>\r\n\t\t\t\t</template>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"contentItem\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t板子厚度\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"thicknessChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.thickness\">{{params.thickness}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择板子厚度</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contentItem\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t铜箔厚度\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"cuThicknessChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.cuThickness\">{{params.cuThickness}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择铜箔厚度</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contentItem\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t阻焊颜色\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"weldColorChoose\">\r\n\t\t\t\t\t\t\t\t<view v-if=\"params.weldColor\" style=\"display: flex;gap: 5rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t{{params.weldColor}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择阻焊颜色</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contentItem\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t字符颜色\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"charColorChoose\">\r\n\t\t\t\t\t\t\t\t<view v-if=\"params.charColor\" style=\"display: flex;gap: 5rpx;align-items: center;\">\r\n\t\t\t\t\t\t\t\t\t{{params.charColor}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择字符颜色</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contentItem\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t阻焊覆盖\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"coverChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.cover\">{{params.cover}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contentItem\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t测试方式\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"testChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.test\">{{params.test}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择测试方式</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contentItem\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t焊盘喷镀\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"surfaceChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.surface\">{{params.surface}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contentItem\" v-if=\"!JSON.parse(choosePriceModel.thermalConductivity).map(item=>item.name).includes('不显示')\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t导热\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"thermalConductivityChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.thermalConductivity\">{{params.thermalConductivity}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contentItem\" v-if=\"!JSON.parse(choosePriceModel.molding).map(item=>item.name).includes('不显示')\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t成型方式\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"moldingChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.molding\">{{params.molding}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-collapse-item>\r\n\t\t\t<uni-collapse-item titleBorder=\"none\" class=\"collItem\">\r\n\t\t\t\t<template v-slot:title>\r\n\t\t\t\t\t<text class=\"title\">个性化服务</text>\r\n\t\t\t\t</template>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"contentItem\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t开票方式\r\n\t\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"params\" @click=\"billTypeChoose\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"params.billType\">{{params.billType}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择</text>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"item\">\r\n\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t订单交期\r\n\t\t\t\t\t\t\t<image class=\"wenhao\" src=\"../../static/images/work/wenhao.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"params\" @click=\"deliveryDateChoose\">\r\n\t\t\t\t\t\t\t<text v-if=\"params.deliveryDate\">{{params.deliveryDate}}</text>\r\n\t\t\t\t\t\t\t<text v-else style=\"color: darkgray;\">请选择订单交期</text>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"15\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</uni-collapse-item>\r\n\t\t</uni-collapse>\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t<u-popup :show=\"chooseNameShow\" @close=\"chooseNamePopupClose\"\r\n\t\tmode=\"bottom\" round=\"10\">\r\n\t\t    <view style=\"height: 50vh;background-color: rgb(247,245,242);border-top-right-radius: 16rpx;border-top-left-radius: 16rpx;\">\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;padding: 16rpx 30rpx;\">\r\n\t\t\t\t\t<text style=\"color: rgba(0, 0, 0, 0.4);font-size: 32rpx;\" @click=\"chooseNamePopupClose\">取消</text>\r\n\t\t\t\t\t<text style=\"font-size: 36rpx;font-weight: bold;\">板材类别</text>\r\n\t\t\t\t\t<text style=\"color: rgb(255,153,0);font-size: 32rpx;\" @click=\"chooseNameConfirm\">确定</text>\r\n\t\t\t\t</view>\r\n\t\t        <view style=\"display: flex;width: 95vw;margin: 0 auto;justify-content: space-between;\r\n\t\t\t\t    justify-content: space-between;flex-wrap: wrap;margin-top: 20rpx;\">\r\n\t\t        \t<view @click=\"chooseType(item)\" v-for=\"item in typeList\" :key=\"item.id\" :style=\"choosedType.id == item.id ? 'border:1rpx solid rgb(255,153,0);background-color: rgba(255,153,0,0.05);box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);':''\" style=\"width: 46vw;background-color: #fff;display: flex;\r\n\t\t\t\t\t     flex-direction: column;align-items: center;padding: 10rpx;margin-bottom: 20rpx;border-radius: 12rpx;box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t\t\t\t\t position: relative;border:1rpx solid #fff;\">\r\n\t\t        \t\t<image :src=\"baseUrl + item.pic\" style=\"height: 100rpx;width: 200rpx;\"/>\r\n\t\t\t\t\t\t<text style=\"margin-top: 10rpx;\">{{item.name}}</text>\r\n\t\t\t\t\t\t<div style=\"position: absolute;right: 10rpx;top: 10rpx;\">\r\n\t\t\t\t\t\t\t<div v-if=\"choosedType.id != item.id\" style=\"border-radius: 50%;width: 30rpx;height: 30rpx;border: 1rpx solid darkgray;\"></div>\r\n\t\t\t\t\t\t\t<image v-else src=\"../../static/images/work/selected.png\" style=\"width: 30rpx;height: 30rpx;\"></image>\r\n\t\t\t\t\t\t</div>\r\n\t\t        \t</view>\r\n\t\t        </view>\r\n\t\t    </view>\r\n\t\t</u-popup>\r\n\t\t\r\n\t\t<u-popup :show=\"choosePopupShow\" @close=\"choosePopupClose\" mode=\"bottom\" round=\"10\">\r\n\t\t    <scroll-view scroll-y=\"true\" style=\"background-color: rgb(247,245,242);border-top-right-radius: 16rpx;border-top-left-radius: 16rpx;\" :style=\"{height:popupHeight+'vh'}\">\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;padding: 16rpx 30rpx;\">\r\n\t\t\t\t\t<text style=\"color: rgba(0, 0, 0, 0.4);font-size: 32rpx;\" @click=\"choosePopupClose\">取消</text>\r\n\t\t\t\t\t<text style=\"font-size: 36rpx;font-weight: bold;\">{{popupTitle}}</text>\r\n\t\t\t\t\t<text style=\"color: rgb(255,153,0);font-size: 32rpx;\" @click=\"choosePopupConfirm\">确定</text>\r\n\t\t\t\t</view>\r\n\t\t        <view style=\"display: flex;width: 95vw;margin: 0 auto;justify-content: space-between;\r\n\t\t\t\t    justify-content: flex-start;flex-wrap: wrap;margin-top: 20rpx;gap: 10rpx;\">\r\n\t\t        \t<view @click=\"choosePopup(item)\" v-for=\"item,index in popupData\" :key=\"index\" :style=\"chooseData[popupKey] == item.text ? 'border:1rpx solid rgb(255,153,0);background-color: rgba(255,153,0,0.05);box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);padding: 10rpx '+padding+'rpx;':'padding: 10rpx '+padding+'rpx;'\" style=\"background-color: #fff;display: flex;align-items: center;\r\n\t\t\t\t\tmargin-bottom: 10rpx;border-radius: 6rpx;box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);justify-content: center;\t\r\n\t\t\t\t\t\t position: relative;text-align: center;border: 1rpx solid #fff;flex-grow: 1;\">\r\n\t\t\t\t\t\t<text style=\"min-width: 80rpx;\">{{item.text}}</text>\r\n\t\t\t\t\t\t<image style=\"width: 40rpx;height: 40rpx;position: absolute;right: 0;bottom: 0;\" src=\"../../static/images/work/selected2.png\" v-if=\"chooseData[popupKey] == item.text\" />\r\n\t\t        \t</view>\r\n\t\t        </view>\r\n\t\t\t\t<view class=\"\" v-if=\"popupKey == 'num'\">\r\n\t\t\t\t\t<text style=\"margin-left: 2.5vw;\">其他数量:</text>\r\n\t\t\t\t\t<input @input=\"numInput\" type=\"number\" style=\"display: block;height: 60rpx;border-radius: 6rpx;;font-size: 24rpx;background-color: #fff;width: 90vw;margin: 20rpx auto;padding: 16rpx 20rpx;\" placeholder=\"请输入100的倍数\" />\r\n\t\t\t\t\t<text style=\"margin-left: 2.5vw;color: rgba(255,153,0,0.6);font-size: 30rpx;\">* 数量必须是100的倍数</text>\r\n\t\t\t\t</view>\r\n\t\t    </scroll-view>\r\n\t\t</u-popup>\r\n\t\t\r\n\t\t<!-- <u-popup :show=\"allCheckPopupShow\" @close=\"allCheckPopupClose\" mode=\"bottom\" round=\"10\">\r\n\t\t    <view style=\"background-color: rgb(247,245,242);border-top-right-radius: 16rpx;border-top-left-radius: 16rpx;\" :style=\"{height:popupHeight+'vh'}\">\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;padding: 16rpx 30rpx;\">\r\n\t\t\t\t\t<text style=\"color: rgba(0, 0, 0, 0.4);font-size: 32rpx;\" @click=\"choosePopupClose\">取消</text>\r\n\t\t\t\t\t<text style=\"font-size: 36rpx;font-weight: bold;\">{{popupTitle}}</text>\r\n\t\t\t\t\t<text style=\"color: rgb(255,153,0);font-size: 32rpx;\" @click=\"choosePopupConfirm\">确定</text>\r\n\t\t\t\t</view>\r\n\t\t        <view style=\"display: flex;width: 95vw;margin: 0 auto;justify-content: space-between;\r\n\t\t\t\t    justify-content: flex-start;flex-wrap: wrap;margin-top: 20rpx;gap: 10rpx;\">\r\n\t\t        \t<view @click=\"allChoosePopup(item)\" v-for=\"item,index in popupData\" :key=\"index\" :style=\"chooseData[popupKey].includes(item.text) ? 'border:1rpx solid rgb(255,153,0);background-color: rgba(255,153,0,0.05);box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);padding: 10rpx '+padding+'rpx;':'padding: 10rpx '+padding+'rpx;'\" style=\"background-color: #fff;display: flex;align-items: center;\r\n\t\t\t\t\tmargin-bottom: 10rpx;border-radius: 6rpx;box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);justify-content: center;\t\r\n\t\t\t\t\t\t position: relative;text-align: center;border: 1rpx solid #fff;flex-grow: 1;\">\r\n\t\t\t\t\t\t<div :style=\"{backgroundColor:item.color}\" style=\"display: inline-block;border: 1rpx solid darkgray;border-radius: 50%;width: 20rpx;height: 20rpx;\" v-if=\"item.color\"></div>\r\n\t\t\t\t\t\t<text style=\"min-width: 80rpx;\">{{item.text}}</text>\r\n\t\t\t\t\t\t<image style=\"width: 40rpx;height: 40rpx;position: absolute;right: 0;bottom: 0;\" src=\"../../static/images/work/selected2.png\" v-if=\"chooseData[popupKey].includes(item.text)\" />\r\n\t\t        \t</view>\r\n\t\t        </view>\r\n\t\t    </view>\r\n\t\t</u-popup> -->\r\n\t\t\r\n\t\t<view class=\"bottomBtn\">\r\n\t\t\t<view class=\"\">\r\n\t\t\t\t<u-button type=\"info\" text=\"重置\" @click=\"chongzhi\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"\">\r\n\t\t\t\t<u-button type=\"warning\" :text=\"params.isBack==1?'返单':'立即计价'\" @click=\"next\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { baseUrl } from \"../../config\"\r\n\timport {listType} from \"@/api/pcb/type.js\"\r\n\timport {toast} from \"@/utils/common.js\"\r\n\timport {listPriceModel} from \"@/api/pcb/priceModel.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcollapseValue:['0'],\r\n\t\t\t\tchooseNameShow:false,\r\n\t\t\t\tbaseUrl,\r\n\t\t\t\tparams:{\r\n\t\t\t\t\tlayer:'',\r\n\t\t\t\t\ttype:'',\r\n\t\t\t\t\ttypeId:'',\r\n\t\t\t\t\tnum:'',\r\n\t\t\t\t\tthickness:'',\r\n\t\t\t\t\tcuThickness:'',\r\n\t\t\t\t\ttest:'',\r\n\t\t\t\t\tcharColor:'',\r\n\t\t\t\t\tweldColor:'',\r\n\t\t\t\t\tcover:'',\r\n\t\t\t\t\tmolding:'',\r\n\t\t\t\t\tsurface:'',\r\n\t\t\t\t\toutWay:'',\r\n\t\t\t\t\tpcbWidth:'',\r\n\t\t\t\t\tpcbHeight:'',\r\n\t\t\t\t\tarea:'',\r\n\t\t\t\t\tprice:'',\r\n\t\t\t\t\tdeliveryDate:'',\r\n\t\t\t\t\tbillType:'',\r\n\t\t\t\t\tthermalConductivity:'',\r\n\t\t\t\t\tstyleCount1:1,\r\n\t\t\t\t\tstyleCount2:1,\r\n\t\t\t\t\tmodelName:'',\r\n\t\t\t\t\tisBack:2\r\n\t\t\t\t},\r\n\t\t\t\ttypeList:[],\r\n\t\t\t\tchoosedType:{},\r\n\t\t\t\tchoosePopupShow:false,\r\n\t\t\t\tallCheckPopupShow:false,\r\n\t\t\t\tpopupTitle:'',\r\n\t\t\t\tpopupData:[],\r\n\t\t\t\tchooseData:{\r\n\t\t\t\t\tlayer:'',\r\n\t\t\t\t\tnum:'',\r\n\t\t\t\t\tthickness:'',\r\n\t\t\t\t\tcuThickness:'',\r\n\t\t\t\t\ttest:'',\r\n\t\t\t\t\tcharColor:'',\r\n\t\t\t\t\tweldColor:'',\r\n\t\t\t\t\tcover:'',\r\n\t\t\t\t\tmolding:'',\r\n\t\t\t\t\tsurface:'',\r\n\t\t\t\t\toutWay:'',\r\n\t\t\t\t\tdeliveryDate:'',\r\n\t\t\t\t\tbillType:'',\r\n\t\t\t\t\tthermalConductivity:''\r\n\t\t\t\t},\r\n\t\t\t\tpopupKey:'',\r\n\t\t\t\tpadding:45,\r\n\t\t\t\tpopupHeight:40,\r\n\t\t\t\tpriceModel:[],\r\n\t\t\t\tlayerList:[],\r\n\t\t\t\tchoosePriceModel:null,\r\n\t\t\t\tisPCS:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tlistType({pageSize:100000}).then(res=>{\r\n\t\t\t\tthis.typeList = res.rows\r\n\t\t\t})\r\n\t\t\tthis.$noti.add(\"addWorkParams\",this.setParams,this)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tnameChoose(){\r\n\t\t\t\tthis.getGoodsList()\r\n\t\t\t\tthis.chooseNameShow = true\r\n\t\t\t},\r\n\t\t\tchongzhi(){\r\n\t\t\t\tlet paramsKey = Object.keys(this.params)\r\n\t\t\t\tthis.params.isBack = 2\r\n\t\t\t\tparamsKey.forEach(key=>{\r\n\t\t\t\t\tthis.params[key] = ''\r\n\t\t\t\t\tthis.chooseData[key] = ''\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchooseNamePopupClose(){\r\n\t\t\t\tthis.chooseNameShow = false\r\n\t\t\t},\r\n\t\t\tgetGoodsList(){\r\n\t\t\t\tlistType({pageSize:100000}).then(res=>{\r\n\t\t\t\t\tthis.typeList = res.rows\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchooseType(item){\r\n\t\t\t\tthis.choosedType = item\r\n\t\t\t\tlistPriceModel({pageSize:100000,typeId:item.id}).then(res=>{\r\n\t\t\t\t\tthis.priceModel = res.rows\r\n\t\t\t\t\tthis.layerList = [...new Set(this.priceModel.map(item => item.layer))]\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchooseNameConfirm(){\r\n\t\t\t\tthis.params.type = this.choosedType.name\r\n\t\t\t\tthis.params.typeId = this.choosedType.id\r\n\t\t\t\tthis.changeChoosePriceModel()\r\n\t\t\t\tthis.chooseNameShow = false\r\n\t\t\t},\r\n\t\t\tchoosePopupClose(){\r\n\t\t\t\tthis.choosePopupShow = false\r\n\t\t\t\tthis.allCheckPopupShow = false\r\n\t\t\t},\r\n\t\t\tchoosePopupConfirm(){\r\n\t\t\t\tif(this.popupKey == 'surface'){\r\n\t\t\t\t\tthis.params.auPdNi = this.chooseData.auPdNi\r\n\t\t\t\t\tthis.params.au = this.chooseData.au\r\n\t\t\t\t}\r\n\t\t\t\tif(this.popupKey == 'layer'){\r\n\t\t\t\t\tthis.choosePriceModel = this.priceModel.filter(item=>item.layer == this.chooseData[this.popupKey])[0]\r\n\t\t\t\t}\r\n\t\t\t\tif(this.popupKey == 'num'){\r\n\t\t\t\t\tconst texts = this.popupData.map(item=>item.text)\r\n\t\t\t\t\tif(!texts.includes(this.chooseData.num)&&parseInt(this.chooseData.num)%100!=0){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle:\"请输入100的倍数！\",\r\n\t\t\t\t\t\t\ticon:\"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(this.popupKey != 'key'){\r\n\t\t\t\t\tthis.params.isBack = 2\r\n\t\t\t\t}\r\n\t\t\t\tif(this.popupKey == 'outWay'){\r\n\t\t\t\t\tif(this.chooseData[this.popupKey] == '单片出货'){\r\n\t\t\t\t\t\tthis.isPCS = true\r\n\t\t\t\t\t\tthis.params.styleCount1 = 1\r\n\t\t\t\t\t\tthis.params.styleCount2 = 1\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.isPCS = false\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.params[this.popupKey] = this.chooseData[this.popupKey]\r\n\t\t\t\tthis.choosePopupShow = false\r\n\t\t\t\tthis.allCheckPopupShow = false\r\n\t\t\t\tlet keys = ['layer','num','outWay']\r\n\t\t\t\tif(keys.includes(this.popupKey)){\r\n\t\t\t\t\tthis.changeChoosePriceModel()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tallCheckPopupClose(){\r\n\t\t\t\tthis.allCheckPopupShow = false\r\n\t\t\t},\r\n\t\t\tlayerChoose(){\r\n\t\t\t\tthis.popupData = []\r\n\t\t\t\tthis.popupHeight = 60\r\n\t\t\t\tthis.layerList.sort()\r\n\t\t\t\tfor(let layer of this.layerList){\r\n\t\t\t\t\tlet temp = {\r\n\t\t\t\t\t\ttext:layer\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.popupData.push(temp)\r\n\t\t\t\t}\r\n\t\t\t\tthis.popupTitle = '板子层数'\r\n\t\t\t\tthis.choosePopupShow = true\r\n\t\t\t\tthis.popupKey = 'layer'\r\n\t\t\t},\r\n\t\t\tchoosePopup(item){\r\n\t\t\t\tthis.chooseData[this.popupKey] = item.text\r\n\t\t\t},\r\n\t\t\tallChoosePopup(item){\r\n\t\t\t\tlet tempArr = []\r\n\t\t\t\tif(this.chooseData[this.popupKey] != ''){\r\n\t\t\t\t\ttempArr = this.chooseData[this.popupKey].split(',')\r\n\t\t\t\t}\r\n\t\t\t\tif(tempArr.includes(item.text)){\r\n\t\t\t\t\ttempArr.splice(tempArr.indexOf(item.text),1)\r\n\t\t\t\t}else{\r\n\t\t\t\t\ttempArr.push(item.text)\r\n\t\t\t\t}\r\n\t\t\t\tthis.chooseData[this.popupKey] = tempArr.join(',')\r\n\t\t\t},\r\n\t\t\tnumInput(e){\r\n\t\t\t\tthis.chooseData.num = parseInt(e.detail.value)\r\n\t\t\t},\r\n\t\t\tnumChoose(){\r\n\t\t\t\tlet numList = [3,5,10,15,20,25,30,50,75,100,125,150,200,250,300,350,400,450,500,600,700,800,900,1000,1200,1500,1600,1750,2000,2500,3000,3500,4000,4500,5000]\r\n\t\t\t\tthis.initPopupData(numList,'num','板子数量',30,80)\r\n\t\t\t},\r\n\t\t\tthicknessChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet thickness = JSON.parse(this.choosePriceModel.thickness)\r\n\t\t\t\tfor(let i=0;i<thickness.length;i++){\r\n\t\t\t\t\tthickness[i] = thickness[i].toFixed(1)\r\n\t\t\t\t}\r\n\t\t\t\tthis.initPopupData(thickness,'thickness','板子厚度',30,40)\r\n\t\t\t},\r\n\t\t\tcuThicknessChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet cuThickness = JSON.parse(this.choosePriceModel.cuThickness).map(item=>item.name)\r\n\t\t\t\tthis.initPopupData(cuThickness,'cuThickness','铜箔厚度(外层)',30,25)\r\n\t\t\t},\r\n\t\t\tdeliveryDateChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet deliveryDate = JSON.parse(this.choosePriceModel.deliveryDate).map(item=>item.name)\r\n\t\t\t\tthis.initPopupData(deliveryDate,'deliveryDate','订单交期',30,25)\r\n\t\t\t},\r\n\t\t\ttestChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet test = JSON.parse(this.choosePriceModel.test).map(item=>item.name)\r\n\t\t\t\tthis.initPopupData(test,'test','测试方式',30,25)\r\n\t\t\t},\r\n\t\t\toutChoose(){\r\n\t\t\t\t// let outWay = JSON.parse(this.choosePriceModel.outWay).map(item=>item.name)\r\n\t\t\t\tlet outWay = ['单片出货','SET出货']\r\n\t\t\t\tthis.initPopupData(outWay,'outWay','出货方式',30,40)\r\n\t\t\t},\r\n\t\t\tcharColorChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.popupData = []\r\n\t\t\t\tlet text = JSON.parse(this.choosePriceModel.charColor).map(item=>item.name)\r\n\t\t\t\tfor(let i=0;i<text.length;i++){\r\n\t\t\t\t\tlet temp = {\r\n\t\t\t\t\t\ttext:text[i],\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.popupData.push(temp)\r\n\t\t\t\t}\r\n\t\t\t\tthis.popupTitle = '字符颜色'\r\n\t\t\t\tthis.padding = 30\r\n\t\t\t\tthis.popupHeight = 25\r\n\t\t\t\tthis.popupKey = 'charColor'\r\n\t\t\t\tthis.choosePopupShow = true\r\n\t\t\t},\r\n\t\t\tweldColorChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.popupData = []\r\n\t\t\t\tlet text = JSON.parse(this.choosePriceModel.weldColor).map(item=>item.name)\r\n\t\t\t\tfor(let i=0;i<text.length;i++){\r\n\t\t\t\t\tlet temp = {\r\n\t\t\t\t\t\ttext:text[i],\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.popupData.push(temp)\r\n\t\t\t\t}\r\n\t\t\t\tthis.popupTitle = '阻焊颜色'\r\n\t\t\t\tthis.padding = 20\r\n\t\t\t\tthis.popupHeight = 40\r\n\t\t\t\tthis.popupKey = 'weldColor'\r\n\t\t\t\tthis.choosePopupShow = true\r\n\t\t\t},\r\n\t\t\tcoverChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet cover = JSON.parse(this.choosePriceModel.cover).map(item=>item.name)\r\n\t\t\t\tthis.initPopupData(cover,'cover','阻焊覆盖',20,40)\r\n\t\t\t},\r\n\t\t\tmoldingChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet molding = JSON.parse(this.choosePriceModel.molding).map(item=>item.name)\r\n\t\t\t\tthis.initPopupData(molding,'molding','成型方式',50,30)\r\n\t\t\t},\r\n\t\t\tsurfaceChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet surface = JSON.parse(this.choosePriceModel.surface).map(item=>item.name)\r\n\t\t\t\tthis.initPopupData(surface,'surface','焊盘喷镀',30,60)\r\n\t\t\t},\r\n\t\t\tbillTypeChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet billType = JSON.parse(this.choosePriceModel.billType).map(item=>item.name)\r\n\t\t\t\tthis.initPopupData(billType,'billType','开票方式',30,60)\r\n\t\t\t},\r\n\t\t\tthermalConductivityChoose(){\r\n\t\t\t\tif(this.choosePriceModel == null){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:\"请先填写基础基本信息\",\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet thermalConductivity = JSON.parse(this.choosePriceModel.thermalConductivity).map(item=>item.name)\r\n\t\t\t\tthis.initPopupData(thermalConductivity,'thermalConductivity','导热',30,60)\r\n\t\t\t},\r\n\t\t\tinitPopupData(arr,key,title,padding,height){\r\n\t\t\t\tthis.popupData = []\r\n\t\t\t\tthis.popupTitle = title\r\n\t\t\t\tthis.padding = padding\r\n\t\t\t\tthis.popupHeight = height\r\n\t\t\t\tarr.forEach(item=>{\r\n\t\t\t\t\tlet temp = {\r\n\t\t\t\t\t\ttext:item\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.popupData.push(temp)\r\n\t\t\t\t})\r\n\t\t\t\tthis.popupKey = key\r\n\t\t\t\tthis.choosePopupShow = true\r\n\t\t\t},\r\n\t\t\tnext(){\r\n\t\t\t\tconst checks = [\r\n\t\t\t\t\t{ condition: this.params.type === '', message:'请选择板材类型'},\r\n\t\t\t\t\t{ condition: this.params.layer === '', message:'请选择板子层数'},\r\n\t\t\t\t\t{ condition: this.params.pcbWidth === '' || this.params.pcbHeight === '', message:'请输入板子大小'},\r\n\t\t\t\t\t{ condition: this.params.styleCount1 === '' || this.params.styleCount2 === '', message:'请输入拼版款数'},\r\n\t\t\t\t\t{ condition: this.params.num === '', message:'请选择板子数量'},\r\n\t\t\t\t\t{ condition: this.params.outWay === '', message:'请选择出货方式'},\r\n\t\t\t\t\t{ condition: this.params.thickness === '', message:'请选择板子厚度'},\r\n\t\t\t\t\t{ condition: this.params.cuThickness === '', message:'请选择铜箔厚度'},\r\n\t\t\t\t\t{ condition: this.params.weldColor === '', message:'请选择阻焊颜色'},\r\n\t\t\t\t\t{ condition: this.params.charColor === '', message:'请选择字符颜色'},\r\n\t\t\t\t\t{ condition: this.params.cover === '', message:'请选择阻焊覆盖'},\r\n\t\t\t\t\t{ condition: this.params.test === '', message:'请选择测试方式'},\r\n\t\t\t\t\t{ condition: this.params.surface === '', message:'请选择焊盘喷镀'},\r\n\t\t\t\t\t{ condition: this.params.billType === '', message:'请选择开票方式'},\r\n\t\t\t\t]\r\n\t\t\t\tfor(let check of checks){\r\n\t\t\t\t\tif(check.condition){\r\n\t\t\t\t\t\ttoast(check.message)\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(!JSON.parse(this.choosePriceModel.thermalConductivity).map(item=>item.name).includes('不显示') && this.params.thermalConductivity === ''){\r\n\t\t\t\t\ttoast(\"请选择导热\")\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif(!JSON.parse(this.choosePriceModel.molding).map(item=>item.name).includes('不显示') && this.params.molding === ''){\r\n\t\t\t\t\ttoast(\"请选择成型方式\")\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:\"/pages/jiesuan/jiesuan\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchangeChoosePriceModel(){\r\n\t\t\t\tthis.params.isBack = 2\r\n\t\t\t\tif(this.params.type == null || this.params.layer == null || this.params.pcbHeight == null || this.params.pcbWidth == null || this.params.styleCount1 == null || this.params.styleCount2 == null || this.params.num == null || this.params.outWay == null ||\r\n\t\t\t\t  this.params.type == '' || this.params.layer == '' || this.params.pcbHeight == '' || this.params.pcbWidth == '' || this.params.styleCount1 == '' || this.params.styleCount2 == '' || this.params.num == '' || this.params.outWay == ''){\r\n\t\t\t\t\t  this.choosePriceModel = null\r\n\t\t\t\t  }else{\r\n\t\t\t\t\t  let totalArea = (this.params.pcbHeight*this.params.pcbWidth*this.params.num)/1000000\r\n\t\t\t\t\t  if(this.params.outWay == 'SET出货'){\r\n\t\t\t\t\t\t  totalArea = totalArea/(this.params.styleCount1*this.params.styleCount2)\r\n\t\t\t\t\t  }\r\n\t\t\t\t\t  console.log(totalArea);\r\n\t\t\t\t\t  let type = this.typeList.filter(item => item.id == this.params.typeId)[0]\r\n\t\t\t\t\t  for(let i=0;i<type.tblTypeItemList.length;i++){\r\n\t\t\t\t\t\t  if(type.tblTypeItemList[i].maxArea!=null&&totalArea>type.tblTypeItemList[i].minArea&&totalArea<=type.tblTypeItemList[i].maxArea){\r\n\t\t\t\t\t\t\t  listPriceModel({typeItemId:type.tblTypeItemList[i].id,typeId:this.params.typeId,layer:this.params.layer}).then(res=>{\r\n\t\t\t\t\t\t\t\t  this.choosePriceModel = res.rows[0]\r\n\t\t\t\t\t\t\t  })\r\n\t\t\t\t\t\t  }else if(type.tblTypeItemList[i].maxArea == null && totalArea > type.tblTypeItemList[i].minArea){\r\n\t\t\t\t\t\t\t  listPriceModel({typeItemId:type.tblTypeItemList[i].id,typeId:this.params.typeId,layer:this.params.layer}).then(res=>{\r\n\t\t\t\t\t\t\t  \t  this.choosePriceModel = res.rows[0]\r\n\t\t\t\t\t\t\t  })\r\n\t\t\t\t\t\t  }\r\n\t\t\t\t\t  }\r\n\t\t\t\t  }\r\n\t\t\t},\r\n\t\t\tpcbWidthInput(e){\r\n\t\t\t\tthis.changeChoosePriceModel()\r\n\t\t\t},\r\n\t\t\tpcbHeightInput(e){\r\n\t\t\t\tthis.changeChoosePriceModel()\r\n\t\t\t},\r\n\t\t\tstyleCount1Input(e){\r\n\t\t\t\tthis.changeChoosePriceModel()\r\n\t\t\t},\r\n\t\t\tstyleCount2Input(e){\r\n\t\t\t\tthis.changeChoosePriceModel()\r\n\t\t\t},\r\n\t\t\tsetParams(data){\r\n\t\t\t\tthis.params = data\r\n\t\t\t\tthis.chooseData = data\r\n\t\t\t\tthis.changeChoosePriceModel()\r\n\t\t\t\tthis.params.isBack = 1\r\n\t\t\t\tconsole.log(data);\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.page{\r\n\t\tpadding-bottom: 130rpx;\r\n\t\tmin-height: 100vh;\r\n\t\tpadding-top: 1rpx;\r\n\t\t.topBack{\r\n\t\t\tbackground-color: rgb(147,116,84);\r\n\t\t\twidth: 100vw;\r\n\t\t\theight: 280rpx;\r\n\t\t\tposition: absolute;\r\n\t\t\tz-index: -999;\r\n\t\t\tborder-radius: 100% / 0 0 50% 50%;\r\n\t\t}\r\n\t\t.coll{\r\n\t\t\twidth: 95vw;\r\n\t\t\tmargin: 20rpx auto;\r\n\t\t\tbackground-color: rgba(255, 255, 255, 0);\r\n\t\t\t.title{\r\n\t\t\t\tborder-left: 8rpx solid rgb(255,153,0);\r\n\t\t\t\tpadding-left: 20rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t}\r\n\t\t\t.collItem{\r\n\t\t\t\tpadding: 20rpx 10rpx 20rpx 10rpx;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\t.content{\r\n\t\t\t\t\tpadding: 20rpx;\r\n\t\t\t\t\t.item{\r\n\t\t\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\t\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.2);\r\n\t\t\t\t\t\t.label{\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t.wenhao{\r\n\t\t\t\t\t\t\t\twidth: 30rpx;\r\n\t\t\t\t\t\t\t\theight: 30rpx;\r\n\t\t\t\t\t\t\t\tmargin-left: 5rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.params{\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\t\tborder: 1rpx solid rgba(0, 0, 0, 0.1);\r\n\t\t\t\t\t\t\tpadding: 16rpx;\r\n\t\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\t\t\tcolor: rgb(255,153,0);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.paramsInput{\r\n\t\t\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\t\t\tpadding: 6rpx;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t\t\tbackground-color: rgb(239,239,239);\r\n\t\t\t\t\t\t\tinput{\r\n\t\t\t\t\t\t\t\twidth: 250rpx;\r\n\t\t\t\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t\t\t\t\tpadding: 10rpx 15rpx;\r\n\t\t\t\t\t\t\t\tcolor: rgb(255,153,0);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tview{\r\n\t\t\t\t\t\t\t\twidth: 100rpx;\r\n\t\t\t\t\t\t\t\tbackground-color: rgb(245,245,245);\r\n\t\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\tcolor: rgba(0, 0, 0, 0.6);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.bottomBtn{\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0;\r\n\t\t\twidth: 100vw;\r\n\t\t\tbackground-color: rgb(251,249,248);\r\n\t\t\theight: 120rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: flex-start;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 20rpx 2.5vw;\r\n\t\t\tgap: 30rpx;\r\n\t\t\tz-index: 999;\r\n\t\t\tview{\r\n\t\t\t\tflex-grow: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541882006\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}