import request from '@/utils/request'

// 查询板材类别列表
export function listType(query) {
  return request({
    url: '/pcb/type/list',
    method: 'get',
    params: query
  })
}

// 查询板材类别详细
export function getType(id) {
  return request({
    url: '/pcb/type/' + id,
    method: 'get'
  })
}

// 新增板材类别
export function addType(data) {
  return request({
    url: '/pcb/type',
    method: 'post',
    data: data
  })
}

// 修改板材类别
export function updateType(data) {
  return request({
    url: '/pcb/type',
    method: 'put',
    data: data
  })
}

// 删除板材类别
export function delType(id) {
  return request({
    url: '/pcb/type/' + id,
    method: 'delete'
  })
}
