{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/orderDetail/orderDetail.vue?79be", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/orderDetail/orderDetail.vue?8199", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/orderDetail/orderDetail.vue?9fe6", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/orderDetail/orderDetail.vue?8acb", "uni-app:///pages/orderDetail/orderDetail.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/orderDetail/orderDetail.vue?fa61", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/orderDetail/orderDetail.vue?2802"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "detail", "onLoad", "id", "methods", "getDetail", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAsoB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC4I1pB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;IACA;EACA;EACAC,8BAEA;IAAA,IADAC;IAEA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClKA;AAAA;AAAA;AAAA;AAAysC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACA7tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/orderDetail/orderDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/orderDetail/orderDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderDetail.vue?vue&type=template&id=c547daf4&\"\nvar renderjs\nimport script from \"./orderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderDetail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/orderDetail/orderDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=template&id=c547daf4&\"", "var components\ntry {\n  components = {\n    uCollapse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-collapse/u-collapse\" */ \"@/uni_modules/uview-ui/components/u-collapse/u-collapse.vue\"\n      )\n    },\n    uCollapseItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-collapse-item/u-collapse-item\" */ \"@/uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<u-collapse class=\"collapse_content\">\r\n\t\t\t<u-collapse-item title=\"基本信息\" class=\"collapse_item\">\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t订单编号：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.orderNo}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t订单类型：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t批量\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t创建时间：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.createTime}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t订单状态：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t待提交订单\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</u-collapse-item>\r\n\t\t\t<u-collapse-item title=\"参数信息\">\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t出货方式：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.outWay}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t尺寸：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.pcbWidth}}cm*{{detail.pcbHeight}}cm\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t数量：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.num}}  ({{detail.area}}㎡)\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t层数：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.layer}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t拼版款数：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.styleCount1}}*{{detail.styleCount2}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t阻焊颜色：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.weldColor}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t字符颜色：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.charColor}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t焊盘喷镀：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.surface}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t铜箔厚度：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.cuThickness}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t板厚：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.thickness}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t测试方式：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.test}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item_content\" style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view class=\"biaoti\">\r\n\t\t\t\t\t\t成型方式：\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"neirong\">\r\n\t\t\t\t\t\t{{detail.molding}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</u-collapse-item>\r\n\t\t</u-collapse>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetOrder\r\n\t} from \"@/api/pcb/order.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdetail: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad({\r\n\t\t\tid\r\n\t\t}) {\r\n\t\t\tthis.getDetail(id)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetDetail(id) {\r\n\t\t\t\tgetOrder(id).then(res => {\r\n\t\t\t\t\tthis.detail = res.data\r\n\t\t\t\t\tconsole.log(this.detail);\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.page {\r\n\t\t.collapse_content {\r\n\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t.collapse_item {\r\n\t\t\t\t.item_content {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.biaoti {\r\n\t\t\t\t\t\tcolor: rgba(0, 0, 0, 0.2);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881671\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}