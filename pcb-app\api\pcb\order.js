import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/pcb/order/list',
    method: 'get',
    params: query
  })
}


// 查询订单详细
export function getOrder(id) {
  return request({
    url: '/pcb/order/' + id,
    method: 'get'
  })
}

// 查询订单详细
export function payOrder(id) {
  return request({
    url: '/pcb/order/pay/' + id,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/pcb/order',
    method: 'post',
    data: data
  })
}

// 查询价格
export function getPrice(data) {
  return request({
    url: '/pcb/order/getPrice',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/pcb/order',
    method: 'put',
    data: data
  })
}

// 提交订单
export function pay(data) {
  return request({
    url: '/pcb/order/pay',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrder(id) {
  return request({
    url: '/pcb/order/' + id,
    method: 'delete'
  })
}
