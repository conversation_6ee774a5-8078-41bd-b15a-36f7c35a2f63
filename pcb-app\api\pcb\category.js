import request from '@/utils/request'

// 查询PCB分类列表
export function listCategory(query) {
  return request({
    url: '/pcb/category/list',
    method: 'get',
    params: query
  })
}

// 查询PCB分类详细
export function getCategory(id) {
  return request({
    url: '/pcb/category/' + id,
    method: 'get'
  })
}

// 新增PCB分类
export function addCategory(data) {
  return request({
    url: '/pcb/category',
    method: 'post',
    data: data
  })
}

// 修改PCB分类
export function updateCategory(data) {
  return request({
    url: '/pcb/category',
    method: 'put',
    data: data
  })
}

// 删除PCB分类
export function delCategory(id) {
  return request({
    url: '/pcb/category/' + id,
    method: 'delete'
  })
}
