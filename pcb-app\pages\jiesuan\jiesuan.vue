<template>
	<view class="page">
		<view class="topBack"></view>
		<view class="container">
			<view class="title">
				<text>板材类型</text>
				<text>数量</text>
				<text>总价(元)</text>
			</view>
			<view class="content">
				<text>{{params.type}}</text>
				<text>{{params.num}}</text>
				<text>{{priceData.totalPrice}}</text>
			</view>
		</view>
		<view class="bottomBtn">
			<u-transition :show="transitionShow">
			    <view class="detail">
					<view class="">
						<view style="font-size: 28rpx;font-weight: bold;width: 100vw;text-align: center;">
							参数详情
						</view>
						<scroll-view scroll-y="true" style="height: 300rpx;" :show-scrollbar="true" >
							<view style="padding: 0 30rpx;">
								<view style="border-bottom: 1rpx solid darkgray;">
									<view style="font-weight: bold;">
										基本信息
									</view>
									<view style="display: flex;flex-direction: column;gap: 20rpx;margin-top: 20rpx;padding-bottom: 30rpx;">
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">板材类别:</text>
											<text style="font-size: 26rpx;">{{params.type}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">板子层数:</text>
											<text style="font-size: 26rpx;">{{params.layer}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">产品面积:</text>
											<text style="font-size: 26rpx;">{{params.area}}㎡</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">拼版款数:</text>
											<text style="font-size: 26rpx;">{{params.styleCount1}}X{{params.styleCount2}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">板子大小:</text>
											<text style="font-size: 26rpx;">{{params.pcbWidth}}mm * {{params.pcbHeight}}mm</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">板子数量:</text>
											<text style="font-size: 26rpx;">{{params.num}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">出货方式:</text>
											<text style="font-size: 26rpx;">{{params.outWay}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">开票方式:</text>
											<text style="font-size: 26rpx;">{{params.billType}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">订单交期:</text>
											<text style="font-size: 26rpx;">{{params.deliveryDate}}</text>
										</view>
									</view>
								</view>
								<view style="border-bottom: 1rpx solid darkgray;margin-top: 20rpx;">
									<view style="font-weight: bold;">
										工艺信息
									</view>
									<view style="display: flex;flex-direction: column;gap: 20rpx;margin-top: 20rpx;padding-bottom: 30rpx;">
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">板子厚度:</text>
											<text style="font-size: 26rpx;">{{params.thickness}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">铜箔厚度:</text>
											<text style="font-size: 26rpx;">{{params.cuThickness}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">阻焊颜色:</text>
											<text style="font-size: 26rpx;">{{params.weldColor}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">字符颜色:</text>
											<text style="font-size: 26rpx;">{{params.charColor}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">阻焊覆盖:</text>
											<text style="font-size: 26rpx;">{{params.cover}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">焊盘喷锡:</text>
											<text style="font-size: 26rpx;">{{params.surface}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">测试方式:</text>
											<text style="font-size: 26rpx;">{{params.test}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">成型方式:</text>
											<text style="font-size: 26rpx;">{{params.molding}}</text>
										</view>
										<view style="display: flex;gap: 150rpx;align-items: center;">
											<text style="font-size: 24rpx;color: darkgray;">导热系数:</text>
											<text style="font-size: 26rpx;">{{params.thermalConductivity}}</text>
										</view>
									</view>
								</view>
							</view>
						</scroll-view>
					</view>
					<view style="background-color: #fff;padding: 10rpx;width: 90vw;margin: 20rpx auto;border-radius: 6rpx;height: 200rpx;margin-top: 40rpx;">
						<text style="font-weight: bold;font-size: 28rpx;margin-left: 20rpx;">PCB订单备注</text>
						<textarea cols="38" rows="2" v-model="params.remark" class="input" placeholder="有特殊要求,请填写此处"  />
					</view>
				</view>
			</u-transition>
			<view class="content">
				<view class="desc">
					<view class="">
						预估总价(不含税运):
						<text style="color: rgb(255,153,0);font-weight: bold;font-size: 26rpx;">￥{{priceData.totalPrice}}</text>
					</view>
					<view style="color: darkgray;" @click="changeShow">
						明细/详情
					</view>
				</view>
				<view class="">
					<u-button type="warning" :text="params.isBack == 1 ? '下单' : '加入购物车'" @click="addCar" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { addOrder,getPrice } from "@/api/pcb/order.js"
	export default {
		data() {
			return {
				params:{},
				transitionShow:false,
				priceData:{}
			}
		},
		onLoad() {
			let pages = getCurrentPages()
			let page = pages[pages.length - 2]
			let params = page.data.params;
			this.params = params
			this.params.userId = this.$store.state.user.userId
			this.params.prepayId = null
			this.params.status = 0;

			if (this.params.isBack == 1) {
				const now = new Date();
				const year = now.getFullYear();
				const month = (now.getMonth() + 1).toString().padStart(2, '0');
				const day = now.getDate().toString().padStart(2, '0');
				const hours = now.getHours().toString().padStart(2, '0');
				const minutes = now.getMinutes().toString().padStart(2, '0');
				const seconds = now.getSeconds().toString().padStart(2, '0');
				this.params.orderTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			}
			
			getPrice(this.params).then(res=>{
				this.priceData = res.data
				this.params.totalPrice = res.data.totalPrice
				this.params.onlinePrice = res.data.onlinePrice
				this.params.offlinePrice = res.data.offlinePrice
				this.params.area = res.data.area
			})
		},
		methods: {
			changeShow(){
				this.transitionShow = !this.transitionShow
			},
			addCar(){
				addOrder(this.params).then(res=>{
					if(this.params.isBack == 2){
						uni.switchTab({
							url:"/pages/car/car"
						})
					}else{
						uni.showModal({
							title:"成功",
							content:"下单成功，等待审核",
							showCancel:false,
							success: () => {
								uni.switchTab({
									url:"/pages/index"
								})
							}
						})
					}
				})
			}
		},
	}
</script>

<style lang="scss">
	.page{
		overflow: hidden;
		height: 100vh;
		.topBack{
			background-color: rgb(147,116,84);
			width: 100vw;
			height: 150rpx;
			position: absolute;
			z-index: -999;
			border-radius: 100% / 0 0 50% 50%;
		}
		.container{
			width: 95vw;
			border-radius: 12rpx;
			background-color: #fff;
			margin: 30rpx auto;
			padding-top: 15rpx;
			padding-bottom: 200rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			.title{
				display: flex;
				background-color: rgb(247,245,242);
				align-items: center;
				gap: 150rpx;
				padding: 0 80rpx;
				font-size: 20rpx;
				height: 40rpx;
			}
			.content{
				display: flex;
				align-items: center;
				gap: 150rpx;
				font-size: 26rpx;
				height: 60rpx;
				padding: 0 80rpx;
				border: 1rpx solid rgba(0,0,0,0.1);
				width: 95%;
				margin-top: 20rpx;
			}
		}
		.bottomBtn{
			width: 100vw;
			position: fixed;
			z-index: 999;
			bottom: 0;
			.detail{
				width: 100vw;
				height: 600rpx;
				background-color: rgb(247,245,242);
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 30rpx 0;
				.input{
					border: 1rpx solid rgba(0, 0, 0, 0.06);
					border-radius: 6rpx;
					width: 95%;
					height: 80rpx;
					margin: 10rpx auto;
				}
			}
			.content{
				background-color: rgb(249,249,250);
				padding: 20rpx 20rpx;
				font-size: 24rpx;
				display: flex;
				flex-direction: column;
				gap: 10rpx;
				box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
				.desc{
					display: flex;
					justify-content: space-between;
				}
			}
			
		}
	}
</style>
