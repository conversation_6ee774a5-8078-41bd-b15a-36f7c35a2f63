{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/login.vue?fec3", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/login.vue?4680", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/login.vue?2b82", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/login.vue?3a1b", "uni-app:///pages/login.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/login.vue?3ebb", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/login.vue?94d9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "codeUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register", "globalConfig", "isChecked", "loginForm", "username", "password", "code", "uuid", "created", "methods", "handleUserRegister", "handlePrivacy", "handleUserAgrement", "getCode", "WeChatLogin", "uni", "title", "icon", "params", "success", "this_", "complete", "handleLogin", "pwd<PERSON><PERSON><PERSON>", "loginSuccess", "toXieYi", "url", "checkChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7CA;AAAA;AAAA;AAAA;AAAinB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC+CroB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;MACAC;MACA;MACAH;QACAI;UACA3B;YACA2B;cACAD;cACAA;cACAA;cACAE;gBACAA;cACA;YACA;UACA;QACA;QACAC;UAAA;YAAA;cAAA;gBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;gBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACAC;MACAV;QACAW;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnKA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=18804380&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=18804380&\"", "var components\ntry {\n  components = {\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var a0 = {\n    \"border-radius\": \"80rpx\",\n    \"margin-top\": \"50rpx\",\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        a0: a0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"normal-login-container\">\n    <!-- <view class=\"logo-content align-center justify-center flex\">\n      <image style=\"width: 100rpx;height: 100rpx;\" :src=\"globalConfig.appInfo.logo\" mode=\"widthFix\">\n      </image>\n      <text class=\"title\">登录</text>\n    </view> -->\n    <view class=\"login-form-content\">\n      <!-- <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-user icon\"></view>\n        <input v-model=\"loginForm.username\" class=\"input\" type=\"text\" placeholder=\"请输入账号\" maxlength=\"30\" />\n      </view> -->\n      <!-- <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-password icon\"></view>\n        <input v-model=\"loginForm.password\" type=\"password\" class=\"input\" placeholder=\"请输入密码\" maxlength=\"20\" />\n      </view> -->\n      <!-- <view class=\"input-item flex align-center\" style=\"width: 60%;margin: 0px;\" v-if=\"captchaEnabled\">\n        <view class=\"iconfont icon-code icon\"></view>\n        <input v-model=\"loginForm.code\" type=\"number\" class=\"input\" placeholder=\"请输入验证码\" maxlength=\"4\" />\n        <view class=\"login-code\"> \r\n          <image :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"></image>\r\n        </view>\n      </view> -->\n      <!-- <view class=\"action-btn\">\n        <button @click=\"handleLogin\" class=\"login-btn cu-btn block bg-blue lg round\">登录</button>\n      </view> -->\r\n      <!-- <view class=\"reg text-center\" v-if=\"register\">\r\n        <text class=\"text-grey1\">没有账号？</text>\r\n        <text @click=\"handleUserRegister\" class=\"text-blue\">立即注册</text>\r\n      </view> -->\r\n\t  <image src=\"https://wkpcb.funyog.com/prod-api/profile/upload/2025/05/12/loginLogo_20250512103032A001.png\" style=\"width: 100vw;height: 45vh;margin: 0vh auto;margin-bottom: 0;\" />\r\n\t  \r\n\t  <u-button type=\"primary\" text=\"手机号快捷登录\" open-type=\"getPhoneNumber\" @getphonenumber=\"WeChatLogin\" :customStyle=\"{'border-radius': '80rpx','margin-top':'50rpx'}\"></u-button>\r\n\t  <view class=\"foot\" v-if=\"!isLogin\">\r\n\t      <checkbox-group @change=\"checkChange\">\r\n\t          <label>\r\n\t              <checkbox :checked=\"isChecked\" style=\"transform:scale(0.7)\" /><text :class=\"{'ischeck':isChecked}\" @click.stop=\"toXieYi\" style=\"font-size: 24rpx;\">用户隐私协议与信息采集</text>\r\n\t          </label>\r\n\t      </checkbox-group>\r\n\t  </view>\r\n\t  <!-- <button type=\"primary\" open-type=\"getPhoneNumber\" @getphonenumber=\"WeChatLogin\" style=\"margin: 30vh auto;\">手机号快捷登录</button> -->\n    </view>\r\n     \n  </view>\n</template>\n\n<script>\n  import { getCodeImg } from '@/api/login'\n\n  export default {\n    data() {\n      return {\n        codeUrl: \"\",\n        captchaEnabled: true,\r\n        // 用户注册开关\r\n        register: false,\n        globalConfig: getApp().globalData.config,\r\n\t\tisChecked:false,\n        loginForm: {\n          username: \"admin\",\n          password: \"admin123\",\n          code: \"\",\n          uuid: ''\n        }\n      }\n    },\n    created() {\n      this.getCode()\n    },\n    methods: {\r\n      // 用户注册\r\n      handleUserRegister() {\r\n        this.$tab.redirectTo(`/pages/register`)\r\n      },\n      // 隐私协议\n      handlePrivacy() {\n        let site = this.globalConfig.appInfo.agreements[0]\n        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)\n      },\n      // 用户协议\n      handleUserAgrement() {\n        let site = this.globalConfig.appInfo.agreements[1]\n        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)\n      },\n      // 获取图形验证码\n      getCode() {\n        getCodeImg().then(res => {\n          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n          if (this.captchaEnabled) {\n            this.codeUrl = 'data:image/gif;base64,' + res.img\n            this.loginForm.uuid = res.uuid\n          }\n        })\n      },\r\n\t   WeChatLogin(e){\r\n\t\t   if(!this.isChecked){\r\n\t\t\t   uni.showToast({\r\n\t\t\t   \ttitle:\"请先勾选同意隐私协议\",\r\n\t\t\t\ticon:\"none\"\r\n\t\t\t   })\r\n\t\t\t   return\r\n\t\t   }\r\n\t  \tthis.$modal.loading(\"登录中，请耐心等待...\")\r\n\t  \tlet params = {}\r\n\t  \tparams.code = e.detail.code\r\n\t\tconst this_ = this\r\n\t  \tuni.getUserInfo({\r\n\t  \t\tsuccess: (res) => {\r\n\t\t\t\twx.login({\r\n\t\t\t\t\tsuccess(codeRes){\r\n\t\t\t\t\t\tparams.ocode = codeRes.code\r\n\t\t\t\t\t\tparams.avatarUrl = res.userInfo.avatarUrl\r\n\t\t\t\t\t\tparams.nickName = res.userInfo.nickName\r\n\t\t\t\t\t\tthis_.$store.dispatch('WxLogin', params).then(res=>{\r\n\t\t\t\t\t\t\tthis_.loginSuccess()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t  \t\t},\r\n\t  \t\tcomplete: async () => {\r\n\t  \t\t}\r\n\t  \t})\r\n\t  },\n      // 登录方法\n      async handleLogin() {\n        if (this.loginForm.username === \"\") {\n          this.$modal.msgError(\"请输入您的账号\")\n        } else if (this.loginForm.password === \"\") {\n          this.$modal.msgError(\"请输入您的密码\")\n        } else if (this.loginForm.code === \"\" && this.captchaEnabled) {\n          this.$modal.msgError(\"请输入验证码\")\n        } else {\n          this.$modal.loading(\"登录中，请耐心等待...\")\n          this.pwdLogin()\n        }\n      },\n      // 密码登录\n      async pwdLogin() {\n        this.$store.dispatch('Login', this.loginForm).then(() => {\n          this.$modal.closeLoading()\n          this.loginSuccess()\n        }).catch(() => {\n          if (this.captchaEnabled) {\n            this.getCode()\n          }\n        })\n      },\n      // 登录成功后，处理函数\n      loginSuccess(result) {\n        // 设置用户信息\n        this.$store.dispatch('GetInfo').then(res => {\n          this.$tab.reLaunch('/pages/index')\n        })\n      },\r\n\t  toXieYi(){\r\n\t  \tuni.navigateTo({\r\n\t  \t\turl:\"/pages/xieyi/xieyi\"\r\n\t  \t})\r\n\t  },\r\n\t  checkChange(e){\r\n\t\t  this.isChecked = e.detail.value.length>0\r\n\t  }\n    }\n  }\n</script>\n\n<style lang=\"scss\">\n  page {\n    background-color: #ffffff;\n  }\n\n  .normal-login-container {\n    width: 100%;\n\n    .logo-content {\n      width: 100%;\n      font-size: 21px;\n      text-align: center;\n      padding-top: 15%;\n\n      image {\n        border-radius: 4px;\n      }\n\n      .title {\n        margin-left: 10px;\n      }\n    }\n\n    .login-form-content {\n      text-align: center;\n      margin: 20px auto;\n      margin-top: 15%;\n      width: 80%;\n\n      .input-item {\n        margin: 20px auto;\n        background-color: #f5f6f7;\n        height: 45px;\n        border-radius: 20px;\n\n        .icon {\n          font-size: 38rpx;\n          margin-left: 10px;\n          color: #999;\n        }\n\n        .input {\n          width: 100%;\n          font-size: 14px;\n          line-height: 20px;\n          text-align: left;\n          padding-left: 15px;\n        }\n\n      }\n\n      .login-btn {\n        margin-top: 40px;\n        height: 45px;\n      }\n      \r\n      .reg {\r\n        margin-top: 15px;\r\n      }\r\n      \n      .xieyi {\n        color: #333;\n        margin-top: 20px;\n      }\r\n      \r\n      .login-code {\r\n        height: 38px;\r\n        float: right;\r\n      \r\n        .login-code-img {\r\n          height: 38px;\r\n          position: absolute;\r\n          margin-left: 10px;\r\n          width: 200rpx;\r\n        }\r\n      }\n    }\n  }\n\r\n.foot {\r\n\tposition: fixed;\r\n\tbottom: 5vh;\r\n\tleft: 200rpx;\r\n  \tfont-size: 24rpx;\r\n  \tcolor: blue;\r\n\tfont-weight: bold;\r\n  \ttext {\r\n  \t\topacity: .5;\r\n  \t}\r\n    .ischeck {\r\n  \t\topacity: 1;\r\n  \t}\r\n  }\r\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881786\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}