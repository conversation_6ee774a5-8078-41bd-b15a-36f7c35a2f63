<template>
	<view class="pay_page">
		<view class="choose_address" @click="toChooseAddress">
			<view class="choose_btn" v-if="address == null">
				点击选择地址
			</view>
			<view v-else @click="toChooseAddress" class="address">
				<view style="display: flex;justify-content: space-between;">
					<view class="">
						收件人：{{address.name}}
					</view>
					<view class="">
						联系电话：{{address.phone}}
					</view>
				</view>
				<view class="">
					地址：{{address.address}}
				</view>
			</view>
		</view>
		<view class="orderList">
			<view class="orderItem" v-for="item in orderList" :key="item.id">
				<view class="title">
					<view @click="toOrderDetail(item.id)">
						<view style="font-size: 32rpx;font-weight: bold;">
							{{item.orderNo}}
						</view>
						<view class="">
							<uni-icons type="right" size="32rpx"></uni-icons>
						</view>
					</view>
					<view style="color: darkgray;">
						{{item.createTime.substring(0,16)}}
					</view>
				</view>
				<view class="content">
					<view class="detail">
						<view class="detail_title">
							基本信息:
						</view>
						<view class="detail_desc">
							{{item.pcbWidth}}cm*{{item.pcbHeight}}cm {{item.layer}}
						</view>
					</view>
					<view class="detail">
						<view class="detail_title">
							数量:
						</view>
						<view class="detail_desc">
							{{item.num}}
						</view>
					</view>
					<view class="detail">
						<view class="detail_title">
							型号:
						</view>
						<view class="detail_desc">
							{{item.modelName}}
						</view>
					</view>
					<view class="detail">
						<view class="detail_title">
							总价:
						</view>
						<view class="detail_desc">
							￥{{item.totalPrice}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="jiesuan_btn">
			<view style="width: 160rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;">
				<text>总计：</text>
				<text style="color: rgb(255,153,0);font-weight: bold;">￥{{totalPrice}}</text>
			</view>
			<view style="height: 80rpx;">
				<u-button @click="pay" type="warning" text="结算"></u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import { toast } from "../../utils/common"
import {pay} from "@/api/pcb/order.js"
	export default {
		data() {
			return {
				orderList:[],
				address:null,
				totalPrice:0
			}
		},
		onLoad() {
			let pages = getCurrentPages()
			let page = pages[pages.length-2]
			this.orderList = page.data.chooseItem
			this.$noti.add('chooseAddress',this.chooseAddress,this)
			this.orderList.forEach(item=>{
				this.totalPrice += item.totalPrice
			})
		},
		methods: {
			toChooseAddress(){
				uni.navigateTo({
					url:"/pages/mine/myAddress/myAddress?a="+1
				})
			},
			chooseAddress(address){
				this.address = address
				for(let i=0;i<this.orderList.length;i++){
					this.orderList[i].name = address.name
					this.orderList[i].phone = address.phone
					this.orderList[i].address = address.address
				}
			},
			pay(){
				if(this.address==null){
					toast("请选择地址！")
					return
				}else{
					pay(this.orderList).then(res=>{
						uni.showToast({
							title:"请耐心等待审核",
							icon:"success",
							success: () => {
								uni.switchTab({
									url:"/pages/mine/index"
								})
							}
						})
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	.pay_page{
		padding-top: 20rpx;
		.choose_address{
			width: 95vw;
			height: 150rpx;
			padding: 30rpx;
			background-color: #fff;
			border-radius: 12rpx;
			margin: 0 auto;
			.choose_btn{
				width: 180rpx;
				margin: 0 auto;
				height: 100rpx;
				line-height: 100rpx;
			}
			.address{
				
			}
		}
		.jiesuan_btn{
			display: flex;
			justify-content: space-between;
			background-color: #fff;
			width: 100vw;
			padding: 20rpx 40rpx;
			position: fixed;
			bottom: 0;
		}
		.orderList{
			margin-top: 40rpx;
			.orderItem{
				width: 95vw;
				background-color: #fff;
				border-radius: 12rpx;
				margin: 20rpx auto;
				padding: 30rpx 50rpx;
				box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
				.title{
					display: flex;
					justify-content: space-between;
					padding-bottom: 20rpx;
					border-bottom: 1rpx solid rgba(0, 0, 0, 0.2);
					view{
						display: flex;
						justify-content: space-between;
						align-items: center;
						gap: 5rpx;
					}
				}
				.content{
					margin-top: 20rpx;
					display: flex;
					flex-direction: column;
					gap: 20rpx;
					.detail{
						display: flex;
						.detail_title{
							width: 150rpx;
							color: darkgray;
						}
						.detail_desc{
							font-weight: bold;
							.up_btn{
								font-weight: 100;
								font-size: 24rpx;
								background-color: rgb(245,245,245);
								padding: 10rpx;
							}
						}
					}
				}
			}
		}
	}
</style>
