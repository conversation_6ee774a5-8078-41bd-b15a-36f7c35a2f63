{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/goodsCenter/goodsCenter.vue?68df", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/goodsCenter/goodsCenter.vue?82d4", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/goodsCenter/goodsCenter.vue?321f", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/goodsCenter/goodsCenter.vue?d3b2", "uni-app:///pages/goodsCenter/goodsCenter.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/goodsCenter/goodsCenter.vue?315f", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/goodsCenter/goodsCenter.vue?a53a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "categoryList", "goodsList", "baseUrl", "detail", "detailShow", "onLoad", "methods", "getCategoryList", "pageSize", "getGoodsList", "categoryId", "console", "tabsClick", "showDetail", "popupClose"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAsoB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACuC1pB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAC;MAAA;QACA;QACA;UAAA;QAAA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QAAAD;QAAAE;MAAA;QACA;QACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACAF;MACA;IACA;IACAG;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrFA;AAAA;AAAA;AAAA;AAAysC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACA7tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/goodsCenter/goodsCenter.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/goodsCenter/goodsCenter.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./goodsCenter.vue?vue&type=template&id=740c5ec4&\"\nvar renderjs\nimport script from \"./goodsCenter.vue?vue&type=script&lang=js&\"\nexport * from \"./goodsCenter.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goodsCenter.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goodsCenter/goodsCenter.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goodsCenter.vue?vue&type=template&id=740c5ec4&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tabs/u-tabs\" */ \"@/uni_modules/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var a0 = {\n    color: \"#fff\",\n    \"font-size\": \"35rpx\",\n  }\n  var a1 = {\n    color: \"#fff\",\n    \"font-size\": \"35rpx\",\n  }\n  var a2 = {\n    height: \"85rpx\",\n    \"min-width\": \"160rpx\",\n  }\n  var l1 = _vm.__map(_vm.goodsList, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var l0 = item.tag.split(\"，\")\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  var l2 = _vm.detail ? _vm.detail.tag.split(\"，\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        a0: a0,\n        a1: a1,\n        a2: a2,\n        l1: l1,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goodsCenter.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goodsCenter.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<page-meta :page-style=\"'overflow:'+(detailShow?'hidden':'visible')\"></page-meta>\n\t<view class=\"page\">\n\t\t<view class=\"topBack\" />\r\n\t\t<u-tabs class=\"tab\" :list=\"categoryList\" @click=\"tabsClick\" :activeStyle=\"{'color':'#fff','font-size':'35rpx'}\" :inactiveStyle=\"{'color':'#fff','font-size':'35rpx'}\" lineColor=\"rgb(255,153,0)\"\r\n\t\t        lineWidth=\"90rpx\" :itemStyle=\"{'height':'85rpx','min-width':'160rpx'}\" />\r\n\t\t<view class=\"goodsList\">\r\n\t\t\t<view @click=\"showDetail(item.id)\" class=\"goodsItem\" v-for=\"item in goodsList\" :key=\"item.id\">\r\n\t\t\t\t<image :src=\"baseUrl + item.pic\"/>\r\n\t\t\t\t<text class=\"title\">{{item.name}}</text>\r\n\t\t\t\t<view class=\"tag\">\r\n\t\t\t\t\t<span v-for=\"tagItem in item.tag.split('，')\" :key=\"tagItem\">{{tagItem}}</span>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<uni-popup ref=\"popup\" background-color=\"#fff\" class=\"popup\" borderRadius=\"10px 10px 0 0\">\r\n\t\t\t<view class=\"container\" v-if=\"detail\">\r\n\t\t\t\t<image :src=\"baseUrl + detail.pic\" />\r\n\t\t\t\t<text class=\"title\">{{detail.name}}</text>\r\n\t\t\t\t<view class=\"tag\">\r\n\t\t\t\t\t<span v-for=\"tagItem in detail.tag.split('，')\" :key=\"tagItem\">{{tagItem}}</span>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\" style=\"margin-top: 30rpx;\">\r\n\t\t\t\t\t<text>层数/板厚：{{detail.layer}}</text>\r\n\t\t\t\t\t<text>表面处理：{{detail.surface}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t<text>线宽线距：{{detail.width}}</text>\r\n\t\t\t\t\t<text>最小孔距：{{detail.bores}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t<text>技术特点：{{detail.tech}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\r\n\timport { baseUrl } from \"../../config\"\r\n\timport {listCategory} from \"@/api/pcb/category.js\"\r\n\timport {listGoods,getGoods} from \"@/api/pcb/goods.js\"\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcategoryList:[],\r\n\t\t\t\tgoodsList:[],\r\n\t\t\t\tbaseUrl,\r\n\t\t\t\tdetail:null,\r\n\t\t\t\tdetailShow:false\n\t\t\t}\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getCategoryList()\r\n\t\t},\n\t\tmethods: {\n\t\t\tgetCategoryList(){\r\n\t\t\t\tlistCategory({pageSize:10000}).then(res=>{\r\n\t\t\t\t\tthis.categoryList = res.rows\r\n\t\t\t\t\tthis.categoryList.unshift({'name':'全部'})\r\n\t\t\t\t\tthis.getGoodsList(null)\r\n\t\t\t\t})\r\n\t\t\t\t// console.log(this.categoryList);\r\n\t\t\t},\r\n\t\t\tgetGoodsList(categoryId){\r\n\t\t\t\tlistGoods({pageSize:10000,categoryId}).then(res=>{\r\n\t\t\t\t\tthis.goodsList = res.rows\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttabsClick(e){\r\n\t\t\t\tthis.getGoodsList(e.id)\r\n\t\t\t},\r\n\t\t\tshowDetail(id){\r\n\t\t\t\tgetGoods(id).then(res=>{\r\n\t\t\t\t\tthis.detail = res.data\r\n\t\t\t\t\tthis.$refs.popup.open()\r\n\t\t\t\t\tthis.detailShow = true\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tpopupClose(){\r\n\t\t\t\tthis.detailShow = false\r\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\r\n\t.page{\r\n\t\tmin-height: 100vh;\r\n\t\tpadding-top: 1rpx;\r\n\t\t.topBack{\r\n\t\t\tbackground-color: rgb(147,116,84);\r\n\t\t\twidth: 100vw;\r\n\t\t\theight: 280rpx;\r\n\t\t\tposition: absolute;\r\n\t\t\tz-index: -999;\r\n\t\t\tborder-radius: 100% / 0 0 50% 50%;\r\n\t\t}\r\n\t\t.tab{\r\n\t\t\tmargin: 50rpx auto;\r\n\t\t\twidth: 90vw;\r\n\t\t}\r\n\t\t.goodsList{\r\n\t\t\twidth: 95vw;\r\n\t\t\tmargin: 10rpx auto;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\t.goodsItem{\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\twidth: 46vw;\t\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\timage{\r\n\t\t\t\t\twidth: 95%;\r\n\t\t\t\t\theight: 300rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.title{\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.tag{\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\tspan{\r\n\t\t\t\t\t\tborder: solid 1rpx rgb(255,153,0);\r\n\t\t\t\t\t\tcolor: rgb(255,153,0);\r\n\t\t\t\t\t\tbackground-color: rgba(255,153,0,0.1);\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tpadding: 3rpx;\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.popup{\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\t.container{\r\n\t\t\t\twidth: 100vw;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\theight: 800rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\timage{\r\n\t\t\t\t\twidth:100%;\r\n\t\t\t\t\theight: 400rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.title{\r\n\t\t\t\t\tmargin-top: 50rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.tag{\r\n\t\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\t\tspan{\r\n\t\t\t\t\t\tborder: solid 1rpx rgb(255,153,0);\r\n\t\t\t\t\t\tcolor: rgb(255,153,0);\r\n\t\t\t\t\t\tbackground-color: rgba(255,153,0,0.1);\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.desc{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\twidth: 70vw;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goodsCenter.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goodsCenter.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881846\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}