{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myOrder/myOrder.vue?35d2", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myOrder/myOrder.vue?5537", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myOrder/myOrder.vue?2630", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myOrder/myOrder.vue?93e2", "uni-app:///pages/mine/myOrder/myOrder.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myOrder/myOrder.vue?20fc", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/myOrder/myOrder.vue?6233"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userId", "current", "tabsList", "name", "status", "orderList", "params", "queryParams", "isOrder", "pageSize", "modelName", "onLoad", "methods", "getOrderList", "searchInput", "tabsClick", "tabsChange", "payOrder", "console", "uni", "title", "mask", "item", "success", "timeStamp", "package", "nonceStr", "paySign", "signType", "fail", "content", "showCancel", "s<PERSON><PERSON><PERSON>", "toOrderDetail", "url", "back", "editable", "placeholderText", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AAC4K;AAC5K,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAAipB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACkHrqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC,WACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;QACAP;QACAQ;QACAJ;QACAK;QACAC;MACA;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC,qCAEA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACA;QACAC;QACA5B;UACA6B;YACAD;YACA;cACAH;cACAzB;gBACA8B;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC,0BACA;gBACAN;kBACAJ;oBACAC;oBACAU;oBACAC;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;QACA;UACAZ;UACAzB;YACA8B;YACAC;YACAC;YACAC;YACAC;YACAC,0BACA;YACAN;cACAJ;gBACAC;gBACAU;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACAV;MACA;IACA;IACAW;MACAd;QACAe;MACA;IACA;IACAC;MAAA;MACA;MACAhB;QACAC;QACAU;QACAM;QACAC;QACAd;UACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACAL;YACAC;cACAC;cACAC;YACA;YACAiB;cACAnB;gBACAe;gBACAX;kBACAJ;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxRA;AAAA;AAAA;AAAA;AAAguC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACApvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/myOrder/myOrder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/myOrder/myOrder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myOrder.vue?vue&type=template&id=4aaa3eb2&\"\nvar renderjs\nimport script from \"./myOrder.vue?vue&type=script&lang=js&\"\nexport * from \"./myOrder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myOrder.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/myOrder/myOrder.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myOrder.vue?vue&type=template&id=4aaa3eb2&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tabs/u-tabs\" */ \"@/uni_modules/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uniSearchBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar\" */ \"@/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tag/u-tag\" */ \"@/uni_modules/uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var a0 = {\n    color: \"#fff\",\n    \"font-size\": \"35rpx\",\n    \"font-weight\": \"bold\",\n  }\n  var a1 = {\n    color: \"#fff\",\n    \"font-size\": \"30rpx\",\n  }\n  var a2 = {\n    height: \"85rpx\",\n    \"min-width\": \"100rpx\",\n  }\n  var g0 = _vm.orderList.length\n  var l0 = !(g0 <= 0)\n    ? _vm.__map(_vm.orderList, function (item, __i0__) {\n        var $orig = _vm.__get_orig(item)\n        var g1 = item.orderTime.substring(0, 16)\n        return {\n          $orig: $orig,\n          g1: g1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        a0: a0,\n        a1: a1,\n        a2: a2,\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myOrder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myOrder.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"topBack\" />\r\n\t\t<u-tabs class=\"tab\" :list=\"tabsList\" @click=\"tabsClick\"  @change=\"tabsChange\" :current=\"current\"\r\n\t\t        :activeStyle=\"{'color':'#fff','font-size':'35rpx','font-weight': 'bold'}\" \r\n\t\t        :inactiveStyle=\"{'color':'#fff','font-size':'30rpx'}\" lineColor=\"rgb(255,153,0)\"\r\n\t\t        lineWidth=\"60rpx\" :itemStyle=\"{'height':'85rpx','min-width':'100rpx'}\" />\r\n\t\t<view style=\"width: 50vw;margin: 0 auto;\">\r\n\t\t\t<uni-search-bar :focus=\"true\" @input=\"searchInput\" cancelButton=\"none\" />\r\n\t\t</view>\r\n\t\t<view class=\"empty\" v-if=\"orderList.length<=0\">\r\n\t\t\t<image src=\"../../../static/images/myOrder/empty.png\" />\r\n\t\t\t<text>暂无记录</text>\r\n\t\t</view>\r\n\t\t<view class=\"orderList\" v-else>\r\n\t\t\t<view class=\"orderItem\" v-for=\"item in orderList\" :key=\"item.id\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<text>{{item.orderNo}}</text>\r\n\t\t\t\t\t<u-tag text=\"审核中\" type=\"info\" v-if=\"item.status == 0\"></u-tag>\r\n\t\t\t\t\t<u-tag text=\"待付款\" type=\"info\" v-if=\"item.status == 1\"></u-tag>\r\n\t\t\t\t\t<u-tag text=\"待发货\" v-if=\"item.status == 2\"></u-tag>\r\n\t\t\t\t\t<u-tag text=\"待收货\" type=\"warning\" v-if=\"item.status == 3\"></u-tag>\r\n\t\t\t\t\t<u-tag text=\"已完成\" type=\"success\" v-if=\"item.status == 4\"></u-tag>\r\n\t\t\t\t\t<u-tag text=\"已取消\" type=\"error\" v-if=\"item.status == 5 && item.auditStatus!=2\"></u-tag>\r\n\t\t\t\t\t<u-tag text=\"审核未通过\" type=\"error\" v-if=\"item.status == 5 && item.auditStatus==2\"></u-tag>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"content_item\" style=\"margin-top: 10rpx;\">\r\n\t\t\t\t\t\t<view style=\"font-size: 26rpx;color: rgba(0, 0, 0, 0.5);\">\r\n\t\t\t\t\t\t\t型号：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t{{item.modelName}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content_item\" style=\"margin-top: 10rpx;\">\r\n\t\t\t\t\t\t<view style=\"font-size: 26rpx;color: rgba(0, 0, 0, 0.5);\">\r\n\t\t\t\t\t\t\t板材类型：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t{{item.type}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content_item\" style=\"\">\r\n\t\t\t\t\t\t<view style=\"font-size: 26rpx;color: rgba(0, 0, 0, 0.5);\">\r\n\t\t\t\t\t\t\t数量：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t{{item.num}}/pcs\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content_item\" style=\"\">\r\n\t\t\t\t\t\t<view style=\"font-size: 26rpx;color: rgba(0, 0, 0, 0.5);\">\r\n\t\t\t\t\t\t\t价格：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"color: rgb(255,153,0);font-weight: bold;\">\r\n\t\t\t\t\t\t\t￥{{item.totalPrice}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content_item\" style=\"\" v-if=\"item.auditContent\">\r\n\t\t\t\t\t\t<view style=\"font-size: 26rpx;color: rgba(0, 0, 0, 0.5);\">\r\n\t\t\t\t\t\t\t审核意见：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"\">\r\n\t\t\t\t\t\t\t{{item.auditContent}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content_item\" style=\"\" v-if=\"item.courierType == 1\">\r\n\t\t\t\t\t\t<view style=\"font-size: 26rpx;color: rgba(0, 0, 0, 0.5);\">\r\n\t\t\t\t\t\t\t快递单号：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"\">\r\n\t\t\t\t\t\t\t{{item.courierNo}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content_item\" style=\"margin-top: 10rpx;\" v-if=\"item.courierType == 2\">\r\n\t\t\t\t\t\t<view style=\"font-size: 26rpx;color: rgba(0, 0, 0, 0.5);\">\r\n\t\t\t\t\t\t\t物流：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t厂商独立运输\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content_item\" style=\"\">\r\n\t\t\t\t\t\t<view style=\"font-size: 26rpx;color: rgba(0, 0, 0, 0.5);\">\r\n\t\t\t\t\t\t\t下单时间：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"font-size: 24rpx;color: darkgray;\">\r\n\t\t\t\t\t\t\t{{item.orderTime.substring(0,16)}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn\">\r\n\t\t\t\t\t<view style=\"display: flex;gap: 30rpx;margin-right: 0rpx;flex-direction: row-reverse;margin-right: 10rpx;\">\r\n\t\t\t\t\t\t<view @click=\"toOrderDetail(item.id)\" style=\"background-color: rgb(59,156,255);color: #fff;text-align: center;width: 90rpx;height: 40rpx;line-height: 40rpx;border-radius: 5rpx;font-size: 20rpx;\">\r\n\t\t\t\t\t\t\t查看\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"item.status == 1\" @click.stop=\"payOrder(item)\" style=\"background-color: rgb(249,174,61);color: #fff;text-align: center;width: 90rpx;height: 40rpx;line-height: 40rpx;border-radius: 5rpx;font-size: 20rpx;\">\r\n\t\t\t\t\t\t\t付款\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"item.status == 3\" @click.stop=\"shouhuo(item)\" style=\"background-color: rgb(249,174,61);color: #fff;text-align: center;width: 90rpx;height: 40rpx;line-height: 40rpx;border-radius: 5rpx;font-size: 20rpx;\">\r\n\t\t\t\t\t\t\t确认收货\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"item.status == 4\" @click.stop=\"back(item)\" style=\"background-color: rgb(249,174,61);color: #fff;text-align: center;width: 90rpx;height: 40rpx;line-height: 40rpx;border-radius: 5rpx;font-size: 20rpx;\">\r\n\t\t\t\t\t\t\t返单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {listOrder,updateOrder,payOrder} from \"@/api/pcb/order.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserId:'',\r\n\t\t\t\tcurrent:0,\r\n\t\t\t\ttabsList:[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname:'全部',\r\n\t\t\t\t\t\tstatus:null\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname:'待付款',\r\n\t\t\t\t\t\tstatus:1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname:'待发货',\r\n\t\t\t\t\t\tstatus:2\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname:'待收货',\r\n\t\t\t\t\t\tstatus:3\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname:'已完成',\r\n\t\t\t\t\t\tstatus:4\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname:'已取消',\r\n\t\t\t\t\t\tstatus:5\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\torderList:[],\r\n\t\t\t\tparams:{},\r\n\t\t\t\tqueryParams:{\r\n\t\t\t\t\tuserId:this.$store.state.user.userId,\r\n\t\t\t\t\tisOrder:1,\r\n\t\t\t\t\tstatus:null,\r\n\t\t\t\t\tpageSize:1000000,\r\n\t\t\t\t\tmodelName:''\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad({status}) {\r\n\t\t\tlet current = parseInt(status)+1\r\n\t\t\tthis.current = current\r\n\t\t\tthis.userId = this.$store.state.user.userId\r\n\t\t\tthis.queryParams.status = this.tabsList[current].status\r\n\t\t\tthis.getOrderList()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetOrderList(){\t\r\n\t\t\t\tlistOrder(this.queryParams).then(res=>{\r\n\t\t\t\t\tthis.orderList = res.rows\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsearchInput(e){\r\n\t\t\t\tthis.queryParams.modelName = e\r\n\t\t\t\tthis.getOrderList()\r\n\t\t\t},\r\n\t\t\ttabsClick(item){\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\ttabsChange(e){\r\n\t\t\t\tthis.current = e.index\r\n\t\t\t\tthis.queryParams.status = e.status\r\n\t\t\t\tthis.getOrderList()\r\n\t\t\t},\r\n\t\t\tpayOrder(item){\r\n\t\t\t\tconsole.log(item);\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle:\"加载中……\",\r\n\t\t\t\t\tmask:true\r\n\t\t\t\t})\r\n\t\t\t\tif(item.prepayId == null){\r\n\t\t\t\t\titem.status = 2\r\n\t\t\t\t\twx.login({\r\n\t\t\t\t\t\tsuccess:loginRes=>{\r\n\t\t\t\t\t\t\titem.code = loginRes.code\r\n\t\t\t\t\t\t\tpayOrder(item.id).then(payRes=>{\r\n\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t\twx.requestPayment({\r\n\t\t\t\t\t\t\t\t\ttimeStamp:payRes.data.timeStamp,\r\n\t\t\t\t\t\t\t\t\tpackage:'prepay_id='+payRes.data.package,\r\n\t\t\t\t\t\t\t\t\tnonceStr:payRes.data.nonceStr,\r\n\t\t\t\t\t\t\t\t\tpaySign:payRes.data.paySign,\r\n\t\t\t\t\t\t\t\t\tsignType:'RSA',\r\n\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tsuccess: (success) => {\r\n\t\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle:\"成功\",\r\n\t\t\t\t\t\t\t\t\t\t\tcontent:\"支付成功！\",\r\n\t\t\t\t\t\t\t\t\t\t\tshowCancel:false,\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tpayOrder(item.id).then(payRes=>{\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\twx.requestPayment({\r\n\t\t\t\t\t\t\ttimeStamp:payRes.data.timeStamp,\r\n\t\t\t\t\t\t\tpackage:'prepay_id='+payRes.data.package,\r\n\t\t\t\t\t\t\tnonceStr:payRes.data.nonceStr,\r\n\t\t\t\t\t\t\tpaySign:payRes.data.paySign,\r\n\t\t\t\t\t\t\tsignType:'RSA',\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tsuccess: (success) => {\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle:\"成功\",\r\n\t\t\t\t\t\t\t\t\tcontent:\"支付成功！\",\r\n\t\t\t\t\t\t\t\t\tshowCancel:false,\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tshouhuo(item){\r\n\t\t\t\titem.status = 4\r\n\t\t\t\tupdateOrder(item)\r\n\t\t\t},\r\n\t\t\ttoOrderDetail(id){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:\"/pages/mine/OrderDetail/OrderDetail?id=\"+id\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tback(item){\r\n\t\t\t\tlet i = JSON.parse(JSON.stringify(item));\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '返单',\r\n\t\t\t\t\tcontent: '',\r\n\t\t\t\t\teditable:true,\r\n\t\t\t\t\tplaceholderText:\"请输入数量\",\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tif(res.confirm){\r\n\t\t\t\t\t\t\tthis.params = i\r\n\t\t\t\t\t\t\tthis.params.num = res.content\r\n\t\t\t\t\t\t\tthis.params.isBack = 1\r\n\t\t\t\t\t\t\tthis.params.id = null\r\n\t\t\t\t\t\t\tthis.params.status = 1\r\n\t\t\t\t\t\t\tthis.params.courierNo = null\r\n\t\t\t\t\t\t\tthis.params.courierType = null\r\n\t\t\t\t\t\t\tthis.params.auditContent = null\r\n\t\t\t\t\t\t\tconsole.log(this.params);\r\n\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\ttitle:\"正在生成订单……\",\r\n\t\t\t\t\t\t\t\tmask:true\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl:\"/pages/jiesuan/jiesuan\",\r\n\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},5000)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.page{\r\n\t\t.topBack{\r\n\t\t\tbackground-color: rgb(147,116,84);\r\n\t\t\twidth: 100vw;\r\n\t\t\theight: 200rpx;\r\n\t\t\tposition: absolute;\r\n\t\t\tz-index: -999;\r\n\t\t\tborder-radius: 100% / 0 0 50% 50%;\r\n\t\t}\r\n\t\t.tab{\r\n\t\t\twidth: 90vw;\r\n\t\t\tmargin: 10rpx auto;\r\n\t\t}\r\n\t\t.empty{\r\n\t\t\twidth: 95vw;\r\n\t\t\tmargin: 20rpx auto;\r\n\t\t\tbox-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t\theight: 400rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\timage{\r\n\t\t\t\twidth: 300rpx;\r\n\t\t\t\theight: 300rpx;\r\n\t\t\t}\r\n\t\t\ttext{\r\n\t\t\t\tcolor: rgba(0, 0, 0, 0.4);\r\n\t\t\t}\r\n\t\t}\r\n\t\t.orderList{\r\n\t\t\t.orderItem{\r\n\t\t\t\twidth: 95vw;\r\n\t\t\t\tmargin: 20rpx auto;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tbox-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\t.title{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.4);\r\n\t\t\t\t\tpadding-bottom: 10rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t\t.content{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tgap: 6rpx;\r\n\t\t\t\t\t.content_item{\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.btn{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: flex-end;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myOrder.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myOrder.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541882017\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}