{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/index.vue?76da", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/index.vue?38e1", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/index.vue?ee41", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/index.vue?efd5", "uni-app:///pages/mine/index.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/index.vue?c4dd", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/index.vue?29f0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "name", "version", "computed", "avatar", "windowHeight", "methods", "handleToInfo", "handleToEditInfo", "handleToSetting", "handleToLogin", "handleToAvatar", "handleLogout", "handleHelp", "handleAbout", "handleBuilding", "toMyOrder", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uni", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACsFppB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChJA;AAAA;AAAA;AAAA;AAAmsC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAvtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4bd6864f&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4bd6864f&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"mine-container\" :style=\"{height: `${windowHeight}px`}\">\r\n    <!--顶部个人信息栏-->\r\n\t<div class=\"topBack\"></div>\r\n    <view class=\"header-section\">\r\n      <view class=\"flex padding justify-between\">\r\n        <view class=\"flex align-center\">\r\n          <view v-if=\"!avatar\" class=\"cu-avatar xl round bg-white\">\r\n            <view class=\"iconfont icon-people text-gray icon\"></view>\r\n          </view>\r\n          <image v-if=\"avatar\" @click=\"handleToAvatar\" :src=\"avatar\" class=\"cu-avatar xl round\" mode=\"widthFix\">\r\n          </image>\r\n          <view v-if=\"!name\" @click=\"handleToLogin\" class=\"login-tip\">\r\n            点击登录\r\n          </view>\r\n          <view v-if=\"name\" @click=\"handleToInfo\" class=\"user-info\">\r\n            <view class=\"u_title\">\r\n              用户名：{{ name }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view @click=\"handleToInfo\" class=\"flex align-center\">\r\n          <text>个人信息</text>\r\n          <view class=\"iconfont icon-right\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\t\r\n\t<view class=\"orderContainer\">\r\n\t\t<view class=\"orderTitle\" @click=\"toMyOrder(-1)\">\r\n\t\t\t<text class=\"title\">我的订单</text>\r\n\t\t\t<view class=\"allOrder\">\r\n\t\t\t\t全部订单\r\n\t\t\t\t<u-icon name=\"arrow-right\" color=\"rgba(0, 0, 0, 0.5)\"></u-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"orderList\">\r\n\t\t\t<view class=\"orderItem\" @click=\"toMyOrder(0)\">\r\n\t\t\t\t<image src=\"../../static/images/mine/daifukuan.png\" />\r\n\t\t\t\t<text>待付款</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderItem\" @click=\"toMyOrder(1)\">\r\n\t\t\t\t<image src=\"../../static/images/mine/daifahuo.png\" />\r\n\t\t\t\t<text>待发货</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderItem\" @click=\"toMyOrder(2)\">\r\n\t\t\t\t<image src=\"../../static/images/mine/daishouhuo.png\" />\r\n\t\t\t\t<text>待收货</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderItem\" @click=\"toMyOrder(3)\">\r\n\t\t\t\t<image src=\"../../static/images/mine/yiwancheng.png\" />\r\n\t\t\t\t<text>已完成</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"orderItem\" @click=\"toMyOrder(4)\">\r\n\t\t\t\t<image src=\"../../static/images/mine/yiquxiao.png\" />\r\n\t\t\t\t<text>已取消</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n    <view class=\"content-section\">\r\n      <view class=\"menu-list\">\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleToEditInfo\">\r\n          <view class=\"menu-item-box\">\r\n            <!-- <view class=\"iconfont icon-user menu-icon\"></view> -->\r\n            <view>编辑资料</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"toMyAddress\">\r\n          <view class=\"menu-item-box\">\r\n            <view>地址管理</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleLogout\">\r\n          <view class=\"menu-item-box\">\r\n            <!-- <view class=\"iconfont icon-setting menu-icon\"></view> -->\r\n            <view>退出登录</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import storage from '@/utils/storage'\r\n  \r\n  export default {\r\n    data() {\r\n      return {\r\n        name: this.$store.state.user.name,\r\n        version: getApp().globalData.config.appInfo.version\r\n      }\r\n    },\r\n    computed: {\r\n      avatar() {\r\n        return this.$store.state.user.avatar\r\n      },\r\n      windowHeight() {\r\n        return uni.getSystemInfoSync().windowHeight - 50\r\n      }\r\n    },\r\n    methods: {\r\n      handleToInfo() {\r\n        this.$tab.navigateTo('/pages/mine/info/index')\r\n      },\r\n      handleToEditInfo() {\r\n        this.$tab.navigateTo('/pages/mine/info/edit')\r\n      },\r\n      handleToSetting() {\r\n        this.$tab.navigateTo('/pages/mine/setting/index')\r\n      },\r\n      handleToLogin() {\r\n        this.$tab.reLaunch('/pages/login')\r\n      },\r\n      handleToAvatar() {\r\n        this.$tab.navigateTo('/pages/mine/avatar/index')\r\n      },\r\n      handleLogout() {\r\n        this.$modal.confirm('确定注销并退出系统吗？').then(() => {\r\n          this.$store.dispatch('LogOut').then(() => {\r\n            this.$tab.reLaunch('/pages/index')\r\n          })\r\n        })\r\n      },\r\n      handleHelp() {\r\n        this.$tab.navigateTo('/pages/mine/help/index')\r\n      },\r\n      handleAbout() {\r\n        this.$tab.navigateTo('/pages/mine/about/index')\r\n      },\r\n      handleBuilding() {\r\n        this.$modal.showToast('模块建设中~')\r\n      },\r\n\t  toMyOrder(status){\r\n\t\t  this.$tab.navigateTo('/pages/mine/myOrder/myOrder?status='+status)\r\n\t  },\r\n\t  toMyAddress(){\r\n\t\t  uni.navigateTo({\r\n\t\t  \turl:\"/pages/mine/myAddress/myAddress\"\r\n\t\t  })\r\n\t  }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  page {\r\n    background-color: #f5f6f7;\r\n  }\r\n  .mine-container {\r\n\tpadding: 0.1rpx;\r\n    width: 100%;\r\n    height: 100%;\r\n\t.topBack{\r\n\t\tbackground-color: rgb(147,116,84);\r\n\t\twidth: 100vw;\r\n\t\theight: 200rpx;\r\n\t\tposition: absolute;\r\n\t\tz-index: -999;\r\n\t\tborder-radius: 100% / 0 0 50% 50%;\r\n\t}\r\n\t.orderContainer{\r\n\t\tbackground-color: #fff;\r\n\t\twidth: 95vw;\r\n\t\tmargin: 20rpx auto;\r\n\t\t// height: 200rpx;\r\n\t\tpadding: 40rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tbox-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t.orderTitle{\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\t.title{\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t\t.allOrder{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tgap: 10rpx;\r\n\t\t\t\tcolor: rgba(0, 0, 0, 0.5);\r\n\t\t\t}\r\n\t\t}\r\n\t\t.orderList{\r\n\t\t\tdisplay: flex;\r\n\t\t\tmargin-top: 30rpx;\r\n\t\t\tgap: 65rpx;\r\n\t\t\t.orderItem{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tgap: 10rpx;\r\n\t\t\t\timage{\r\n\t\t\t\t\twidth: 50rpx;\r\n\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t}\r\n\t\t\t\ttext{\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n    .header-section {\r\n      // padding: 15px 15px 45px 15px;\r\n      background-color: #fff;\r\n\t  width: 95vw;\r\n\t  margin: 20rpx auto;\r\n\t  box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t  border-radius: 12rpx;\r\n\r\n      .login-tip {\r\n        font-size: 18px;\r\n        margin-left: 10px;\r\n      }\r\n\r\n      .cu-avatar {\r\n        border: 2px solid #eaeaea;\r\n\r\n        .icon {\r\n          font-size: 40px;\r\n        }\r\n      }\r\n\r\n      .user-info {\r\n        margin-left: 15px;\r\n\r\n        .u_title {\r\n          font-size: 18px;\r\n          line-height: 30px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .content-section {\r\n      position: relative;\r\n      top: 0px;\r\n\r\n      .mine-actions {\r\n        margin: 15px 15px;\r\n        padding: 20px 0px;\r\n        border-radius: 8px;\r\n        background-color: white;\r\n\r\n        .action-item {\r\n          .icon {\r\n            font-size: 28px;\r\n          }\r\n\r\n          .text {\r\n            display: block;\r\n            font-size: 13px;\r\n            margin: 8px 0px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881920\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}