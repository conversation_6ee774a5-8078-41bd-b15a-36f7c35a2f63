{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/register.vue?a714", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/register.vue?2230", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/register.vue?292b", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/register.vue?4659", "uni-app:///pages/register.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/register.vue?9539", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/register.vue?e801"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "codeUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "globalConfig", "registerForm", "username", "password", "confirmPassword", "code", "uuid", "created", "methods", "handleUserLogin", "getCode", "handleRegister", "register", "uni", "title", "content", "success", "url", "registerSuccess"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAonB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsCxoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAC;oBACAC;oBACAC;oBACAC;sBACA;wBACAH;0BAAAI;wBAAA;sBACA;oBACA;kBACA;gBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrHA;AAAA;AAAA;AAAA;AAA2qC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACA/rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=2339929c&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/register.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=template&id=2339929c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"normal-login-container\">\n    <view class=\"logo-content align-center justify-center flex\">\n      <image style=\"width: 100rpx;height: 100rpx;\" :src=\"globalConfig.appInfo.logo\" mode=\"widthFix\">\n      </image>\n      <text class=\"title\">注册</text>\n    </view>\n    <view class=\"login-form-content\">\n      <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-user icon\"></view>\n        <input v-model=\"registerForm.username\" class=\"input\" type=\"text\" placeholder=\"请输入账号\" maxlength=\"30\" />\n      </view>\n      <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-password icon\"></view>\n        <input v-model=\"registerForm.password\" type=\"password\" class=\"input\" placeholder=\"请输入密码\" maxlength=\"20\" />\n      </view>\n      <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-password icon\"></view>\n        <input v-model=\"registerForm.confirmPassword\" type=\"password\" class=\"input\" placeholder=\"请输入重复密码\" maxlength=\"20\" />\n      </view>\n      <view class=\"input-item flex align-center\" style=\"width: 60%;margin: 0px;\" v-if=\"captchaEnabled\">\n        <view class=\"iconfont icon-code icon\"></view>\n        <input v-model=\"registerForm.code\" type=\"number\" class=\"input\" placeholder=\"请输入验证码\" maxlength=\"4\" />\n        <view class=\"login-code\"> \n          <image :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"></image>\n        </view>\n      </view>\n      <view class=\"action-btn\">\n        <button @click=\"handleRegister()\" class=\"register-btn cu-btn block bg-blue lg round\">注册</button>\n      </view>\n    </view>\n    <view class=\"xieyi text-center\">\n      <text @click=\"handleUserLogin\" class=\"text-blue\">使用已有账号登录</text>\n    </view>\n  </view>\n</template>\n\n<script>\n  import { getCodeImg, register } from '@/api/login'\n\n  export default {\n    data() {\n      return {\n        codeUrl: \"\",\n        captchaEnabled: true,\n        globalConfig: getApp().globalData.config,\n        registerForm: {\n          username: \"\",\n          password: \"\",\n          confirmPassword: \"\",\n          code: \"\",\n          uuid: ''\n        }\n      }\n    },\n    created() {\n      this.getCode()\n    },\n    methods: {\n      // 用户登录\n      handleUserLogin() {\n        this.$tab.navigateTo(`/pages/login`)\n      },\n      // 获取图形验证码\n      getCode() {\n        getCodeImg().then(res => {\n          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n          if (this.captchaEnabled) {\n            this.codeUrl = 'data:image/gif;base64,' + res.img\n            this.registerForm.uuid = res.uuid\n          }\n        })\n      },\n      // 注册方法\n      async handleRegister() {\n        if (this.registerForm.username === \"\") {\n          this.$modal.msgError(\"请输入您的账号\")\n        } else if (this.registerForm.password === \"\") {\n          this.$modal.msgError(\"请输入您的密码\")\n        } else if (this.registerForm.confirmPassword === \"\") {\n          this.$modal.msgError(\"请再次输入您的密码\")\n        } else if (this.registerForm.password !== this.registerForm.confirmPassword) {\n          this.$modal.msgError(\"两次输入的密码不一致\")\n        } else if (this.registerForm.code === \"\" && this.captchaEnabled) {\n          this.$modal.msgError(\"请输入验证码\")\n        } else {\n          this.$modal.loading(\"注册中，请耐心等待...\")\n          this.register()\n        }\n      },\n      // 用户注册\n      async register() {\n        register(this.registerForm).then(res => {\n          this.$modal.closeLoading()\n          uni.showModal({\n          \ttitle: \"系统提示\",\n          \tcontent: \"恭喜你，您的账号 \" + this.registerForm.username + \" 注册成功！\",\n          \tsuccess: function (res) {\n          \t\tif (res.confirm) {\n                uni.redirectTo({ url: `/pages/login` });\n          \t\t}\n          \t}\n          })\n        }).catch(() => {\n          if (this.captchaEnabled) {\n            this.getCode()\n          }\n        })\n      },\n      // 注册成功后，处理函数\n      registerSuccess(result) {\n        // 设置用户信息\n        this.$store.dispatch('GetInfo').then(res => {\n          this.$tab.reLaunch('/pages/index')\n        })\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\">\n  page {\n    background-color: #ffffff;\n  }\n\n  .normal-login-container {\n    width: 100%;\n\n    .logo-content {\n      width: 100%;\n      font-size: 21px;\n      text-align: center;\n      padding-top: 15%;\n\n      image {\n        border-radius: 4px;\n      }\n\n      .title {\n        margin-left: 10px;\n      }\n    }\n\n    .login-form-content {\n      text-align: center;\n      margin: 20px auto;\n      margin-top: 15%;\n      width: 80%;\n\n      .input-item {\n        margin: 20px auto;\n        background-color: #f5f6f7;\n        height: 45px;\n        border-radius: 20px;\n\n        .icon {\n          font-size: 38rpx;\n          margin-left: 10px;\n          color: #999;\n        }\n\n        .input {\n          width: 100%;\n          font-size: 14px;\n          line-height: 20px;\n          text-align: left;\n          padding-left: 15px;\n        }\n\n      }\n\n      .register-btn {\n        margin-top: 40px;\n        height: 45px;\n      }\n\n      .xieyi {\n        color: #333;\n        margin-top: 20px;\n      }\n      \n      .login-code {\n        height: 38px;\n        float: right;\n      \n        .login-code-img {\n          height: 38px;\n          position: absolute;\n          margin-left: 10px;\n          width: 200rpx;\n        }\n      }\n    }\n  }\n\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881766\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}