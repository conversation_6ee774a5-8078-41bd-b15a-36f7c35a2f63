{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/car/car.vue?c8a9", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/car/car.vue?81ad", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/car/car.vue?9b7d", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/car/car.vue?b650", "uni-app:///pages/car/car.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/car/car.vue?59fb", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/car/car.vue?25ec"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderList", "userId", "chooseItem", "totalPrice", "onShow", "methods", "getOrderList", "pageSize", "isOrder", "checkBoxChange", "next", "uni", "url", "<PERSON><PERSON><PERSON><PERSON>", "toWork", "deleteItem", "title", "content", "success", "choose", "count", "console", "icon", "name", "filePath", "item", "allCheckBoxChange", "toOrderDetail", "watch", "newVal"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACc;;;AAGhE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAA8nB,CAAgB,6mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkGlpB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAC;QAAAC;QAAAP;MAAA;QACA;QACA;MACA;IACA;IACAQ;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;QACA;UACA;UACA;QACA;MACA;MACAC;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACAF;QACAC;MACA;IACA;IACAE;MACAH;QACAC;MACA;IACA;IACAG;MAAA;MACA;QACA;QACA;MACA;MACAJ;QACAK;QACAC;QACAC;UACA;YACA;cAAA;YAAA;YACA;cACA;cACA;gBAAA;cAAA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAzB;QACA0B;QACAF;UACAG;UACA;YACAV;cACAK;cACAM;YACA;YACA;UACA;UACAX;YACAK;UACA;UACA;UACA;YACAJ;YACAW;YACAC;UACA;YACAC;YACA;cACAd;cACA;YACA;UACA;YACAA;cACAK;cACAM;YACA;YACAD;UACA;QACA;MACA;IACA;IACAK;MAAA;MACA;QACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACAhB;QACAC;MACA;IACA;EACA;EACAgB;IACA1B;MAAA;MACA;MACA2B;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/OA;AAAA;AAAA;AAAA;AAAisC,CAAgB,4lCAAG,EAAC,C;;;;;;;;;;;ACArtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/car/car.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/car/car.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./car.vue?vue&type=template&id=51e9b630&\"\nvar renderjs\nimport script from \"./car.vue?vue&type=script&lang=js&\"\nexport * from \"./car.vue?vue&type=script&lang=js&\"\nimport style0 from \"./car.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/car/car.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=template&id=51e9b630&\"", "var components\ntry {\n  components = {\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-checkbox/u-checkbox\" */ \"@/uni_modules/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.userId == null || _vm.orderList.length <= 0\n  var g1 = g0 ? _vm.orderList.length <= 0 && _vm.userId : null\n  var l0 = _vm.__map(_vm.orderList, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var g2 = _vm.chooseItem.includes(item)\n    var g3 = item.createTime.substring(0, 16)\n    return {\n      $orig: $orig,\n      g2: g2,\n      g3: g3,\n    }\n  })\n  var g4 = _vm.orderList.length\n  var g5 = g4 > 0 ? _vm.orderList.length : null\n  var g6 = g4 > 0 ? _vm.chooseItem.length : null\n  var g7 = g4 > 0 ? _vm.chooseItem.length : null\n  var g8 = g4 > 0 && g7 > 0 ? _vm.chooseItem.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"topBack\" />\r\n\t\t<view class=\"empty\" v-if=\"userId==null||orderList.length<=0\">\r\n\t\t\t<image src=\"../../static/images/cart/emptyCar.png\" />\r\n\t\t\t<text v-if=\"userId == null\">\r\n\t\t\t\t请点击\r\n\t\t\t\t<text @click=\"toLogin\" style=\"color: rgb(255,153,0);margin: 0 10rpx;\">立即登录</text>\r\n\t\t\t\t查看\r\n\t\t\t</text>\r\n\t\t\t<text v-if=\"orderList.length<=0&&userId\">\r\n\t\t\t\t您的购物车是空的，请点击\r\n\t\t\t\t<text @click=\"toWork\" style=\"color: rgb(255,153,0);margin: 0 10rpx;\">在线计价</text>\r\n\t\t\t\t下单\r\n\t\t\t</text>\r\n\t\t</view>\r\n\t\t<view class=\"orderList\">\r\n\t\t\t<view class=\"orderItem\" v-for=\"item in orderList\" :key=\"item.id\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<view @click=\"toOrderDetail(item.id)\">\r\n\t\t\t\t\t\t<u-checkbox  :checked=\"chooseItem.includes(item)\" @change=\"checkBoxChange(item)\" activeColor=\"rgb(255,153,0)\" />\r\n\t\t\t\t\t\t<view style=\"font-size: 32rpx;font-weight: bold;\">\r\n\t\t\t\t\t\t\t{{item.orderNo}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"32rpx\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"color: darkgray;\">\r\n\t\t\t\t\t\t{{item.createTime.substring(0,16)}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t基本信息:\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail_desc\">\r\n\t\t\t\t\t\t\t{{item.pcbWidth}}mm*{{item.pcbHeight}}mm {{item.layer}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t数量:\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail_desc\">\r\n\t\t\t\t\t\t\t{{item.num}}PCS\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t型号:\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail_desc\">\r\n\t\t\t\t\t\t\t{{item.modelName}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t价格:\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail_desc\">\r\n\t\t\t\t\t\t\t￥{{item.totalPrice}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<view class=\"detail_title\">\r\n\t\t\t\t\t\t\t上传文件:\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail_desc\">\r\n\t\t\t\t\t\t\t<view class=\"up_btn\" @click=\"choose(item)\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.file == null || item.file == ''\">上传文件</text>\r\n\t\t\t\t\t\t\t\t<text v-else>重新选择</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"bottom_container\" v-if=\"orderList.length>0\">\r\n\t\t\t<view class=\"left\">\r\n\t\t\t\t<u-checkbox  label=\"全选\" :checked=\"orderList.length == chooseItem.length\" @change=\"allCheckBoxChange\" activeColor=\"rgb(255,153,0)\" />\r\n\t\t\t\t<view style=\"color: rgba(0,0,0,0.3);\" @click=\"deleteItem\">删除</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"right\">\r\n\t\t\t\t<view style=\"width: 160rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;\">\r\n\t\t\t\t\t<text>总计：</text>\r\n\t\t\t\t\t<text style=\"color: rgb(255,153,0);font-weight: bold;\">￥{{totalPrice}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"height: 80rpx;\">\r\n\t\t\t\t\t<u-button @click=\"next\" type=\"warning\" :text=\"chooseItem.length>0?'下一步('+chooseItem.length+')':'下一步'\"></u-button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\n\t</view>\n</template>\n\n<script>\r\n\timport { listOrder,updateOrder,pay,delOrder } from \"@/api/pcb/order.js\"\r\n\timport upload from '@/utils/upload'\r\n\timport {toast} from \"@/utils/common.js\"\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torderList:[],\r\n\t\t\t\tuserId:null,\r\n\t\t\t\tchooseItem:[],\r\n\t\t\t\ttotalPrice:0\n\t\t\t}\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.userId = this.$store.state.user.userId\r\n\t\t\tthis.getOrderList()\r\n\t\t\tthis.chooseItem = []\r\n\t\t},\n\t\tmethods: {\r\n\t\t\tgetOrderList(){\r\n\t\t\t\tlistOrder({pageSize:100000,isOrder:0,userId:this.userId}).then(res=>{\r\n\t\t\t\t\tthis.chooseItem = []\r\n\t\t\t\t\tthis.orderList = res.rows\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcheckBoxChange(item){\r\n\t\t\t\tif(this.chooseItem.includes(item)){\r\n\t\t\t\t\tthis.chooseItem.splice(this.chooseItem.indexOf(item),1)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.chooseItem.push(item)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tnext(){\r\n\t\t\t\tif(this.chooseItem.length<=0){\r\n\t\t\t\t\ttoast(\"请选择订单\")\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tfor(let i=0;i<this.chooseItem.length;i++){\r\n\t\t\t\t\tif(this.chooseItem[i].file=='' || this.chooseItem[i].file == null){\r\n\t\t\t\t\t\ttoast('请上传文件')\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:\"/pages/payPage/payPage\"\r\n\t\t\t\t})\r\n\t\t\t\t// pay(this.chooseItem).then(res=>{\r\n\t\t\t\t// \tthis.getOrderList()\r\n\t\t\t\t// \tuni.showToast({\r\n\t\t\t\t// \t\ttitle:\"可前往我的订单查看\",\r\n\t\t\t\t// \t\ticon:\"success\"\r\n\t\t\t\t// \t})\r\n\t\t\t\t// })\r\n\t\t\t},\r\n\t\t\ttoLogin(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:\"/pages/login\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoWork(){\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl:\"/pages/work/index\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tdeleteItem(){\r\n\t\t\t\tif(this.chooseItem.length<=0){\r\n\t\t\t\t\ttoast('请选择要删除的订单！')\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle:\"提示\",\r\n\t\t\t\t\tcontent:'确定删除已选中的订单吗？',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif(res.confirm){\r\n\t\t\t\t\t\t\tlet ids = this.chooseItem.map(item=>item.id)\r\n\t\t\t\t\t\t\tdelOrder(ids).then(res=>{\r\n\t\t\t\t\t\t\t\tthis.getOrderList()\r\n\t\t\t\t\t\t\t\tthis.chooseItem = this.chooseItem.filter(item=>!ids.includes(item.id))\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchoose(item){\r\n\t\t\t\twx.chooseMessageFile({\r\n\t\t\t\t\tcount:1,\r\n\t\t\t\t\tsuccess:(res)=>{\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\tif(res.tempFiles[0].size > 52428800){\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle:\"上传大小不能超过50mb\",\r\n\t\t\t\t\t\t\t\ticon:\"none\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle:\"上传中……\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tlet file = res.tempFiles[0]\r\n\t\t\t\t\t\tupload({\r\n\t\t\t\t\t\t\turl:'/common/upload',\r\n\t\t\t\t\t\t\tname:'file',\r\n\t\t\t\t\t\t\tfilePath:file.path\r\n\t\t\t\t\t\t}).then(uploadRes=>{\r\n\t\t\t\t\t\t\titem.file = uploadRes.fileName\r\n\t\t\t\t\t\t\tupdateOrder(item).then(()=>{\r\n\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t\tthis.getOrderList()\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}).catch(err=>{\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle:\"上传失败\",\r\n\t\t\t\t\t\t\t\ticon:\"error\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tallCheckBoxChange(){\r\n\t\t\t\tif(this.chooseItem.length>0){\r\n\t\t\t\t\tthis.chooseItem = []\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.orderList.forEach(item=>{\r\n\t\t\t\t\t\tthis.chooseItem.push(item)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoOrderDetail(id){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:\"/pages/orderDetail/orderDetail?id=\"+id\r\n\t\t\t\t})\r\n\t\t\t}\n\t\t},\r\n\t\twatch:{\r\n\t\t\tchooseItem(oldVal,newVal){\r\n\t\t\t\tthis.totalPrice = 0\r\n\t\t\t\tnewVal.forEach(item=>{\r\n\t\t\t\t\tthis.totalPrice+=item.totalPrice\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\r\n\t.page{\r\n\t\tpadding-top: 0.1rpx;\r\n\t\t.topBack{\r\n\t\t\tbackground-color: rgb(147,116,84);\r\n\t\t\twidth: 100vw;\r\n\t\t\theight: 280rpx;\r\n\t\t\tposition: absolute;\r\n\t\t\tz-index: -999;\r\n\t\t\tborder-radius: 100% / 0 0 50% 50%;\r\n\t\t}\r\n\t\t.empty{\r\n\t\t\twidth: 95vw;\r\n\t\t\tmargin: 20rpx auto;\r\n\t\t\tbox-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t\theight: 400rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\timage{\r\n\t\t\t\twidth: 300rpx;\r\n\t\t\t\theight: 300rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.orderList{\r\n\t\t\tmargin-top: 40rpx;\r\n\t\t\t.orderItem{\r\n\t\t\t\twidth: 95vw;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tmargin: 20rpx auto;\r\n\t\t\t\tpadding: 30rpx 50rpx;\r\n\t\t\t\tbox-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);\r\n\t\t\t\t.title{\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.2);\r\n\t\t\t\t\tview{\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tgap: 5rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.content{\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tgap: 20rpx;\r\n\t\t\t\t\t.detail{\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t.detail_title{\r\n\t\t\t\t\t\t\twidth: 150rpx;\r\n\t\t\t\t\t\t\tcolor: darkgray;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.detail_desc{\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t.up_btn{\r\n\t\t\t\t\t\t\t\tfont-weight: 100;\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tbackground-color: rgb(245,245,245);\r\n\t\t\t\t\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.bottom_container{\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\twidth: 100vw;\r\n\t\t\theight: 140rpx;\r\n\t\t\tpadding: 10rpx 40rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\t.left{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\twidth: 180rpx;\r\n\t\t\t}\r\n\t\t\t.right{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 280rpx;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t}\r\n\t\t}\r\n\t}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881937\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}