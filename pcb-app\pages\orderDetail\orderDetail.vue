<template>
	<view class="page">
		<u-collapse class="collapse_content">
			<u-collapse-item title="基本信息" class="collapse_item">
				<view class="item_content" style="display: flex;justify-content: space-between;">
					<view class="biaoti">
						订单编号：
					</view>
					<view class="neirong">
						{{detail.orderNo}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						订单类型：
					</view>
					<view class="neirong">
						批量
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						创建时间：
					</view>
					<view class="neirong">
						{{detail.createTime}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						订单状态：
					</view>
					<view class="neirong">
						待提交订单
					</view>
				</view>
			</u-collapse-item>
			<u-collapse-item title="参数信息">
				<view class="item_content" style="display: flex;justify-content: space-between;">
					<view class="biaoti">
						出货方式：
					</view>
					<view class="neirong">
						{{detail.outWay}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						尺寸：
					</view>
					<view class="neirong">
						{{detail.pcbWidth}}cm*{{detail.pcbHeight}}cm
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						数量：
					</view>
					<view class="neirong">
						{{detail.num}}  ({{detail.area}}㎡)
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						层数：
					</view>
					<view class="neirong">
						{{detail.layer}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						拼版款数：
					</view>
					<view class="neirong">
						{{detail.styleCount1}}*{{detail.styleCount2}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						阻焊颜色：
					</view>
					<view class="neirong">
						{{detail.weldColor}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						字符颜色：
					</view>
					<view class="neirong">
						{{detail.charColor}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						焊盘喷镀：
					</view>
					<view class="neirong">
						{{detail.surface}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						铜箔厚度：
					</view>
					<view class="neirong">
						{{detail.cuThickness}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						板厚：
					</view>
					<view class="neirong">
						{{detail.thickness}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						测试方式：
					</view>
					<view class="neirong">
						{{detail.test}}
					</view>
				</view>
				<view class="item_content" style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<view class="biaoti">
						成型方式：
					</view>
					<view class="neirong">
						{{detail.molding}}
					</view>
				</view>
			</u-collapse-item>
		</u-collapse>
	</view>
</template>

<script>
	import {
		getOrder
	} from "@/api/pcb/order.js"
	export default {
		data() {
			return {
				detail: []
			}
		},
		onLoad({
			id
		}) {
			this.getDetail(id)
		},
		methods: {
			getDetail(id) {
				getOrder(id).then(res => {
					this.detail = res.data
					console.log(this.detail);
				})
			}
		}
	}
</script>

<style lang="scss">
	.page {
		.collapse_content {
			background-color: #fff;

			.collapse_item {
				.item_content {
					display: flex;
					justify-content: space-between;

					.biaoti {
						color: rgba(0, 0, 0, 0.2);
					}
				}
			}
		}
	}
</style>