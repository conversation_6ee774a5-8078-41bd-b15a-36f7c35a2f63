<template>
	<view class="page">
		<view class="topBack" />
		<u-tabs class="tab" :list="tabsList" @click="tabsClick"  @change="tabsChange" :current="current"
		        :activeStyle="{'color':'#fff','font-size':'35rpx','font-weight': 'bold'}" 
		        :inactiveStyle="{'color':'#fff','font-size':'30rpx'}" lineColor="rgb(255,153,0)"
		        lineWidth="60rpx" :itemStyle="{'height':'85rpx','min-width':'100rpx'}" />
		<view style="width: 50vw;margin: 0 auto;">
			<uni-search-bar :focus="true" @input="searchInput" cancelButton="none" />
		</view>
		<view class="empty" v-if="orderList.length<=0">
			<image src="../../../static/images/myOrder/empty.png" />
			<text>暂无记录</text>
		</view>
		<view class="orderList" v-else>
			<view class="orderItem" v-for="item in orderList" :key="item.id">
				<view class="title">
					<text>{{item.orderNo}}</text>
					<u-tag text="审核中" type="info" v-if="item.status == 0"></u-tag>
					<u-tag text="待付款" type="info" v-if="item.status == 1"></u-tag>
					<u-tag text="待发货" v-if="item.status == 2"></u-tag>
					<u-tag text="待收货" type="warning" v-if="item.status == 3"></u-tag>
					<u-tag text="已完成" type="success" v-if="item.status == 4"></u-tag>
					<u-tag text="已取消" type="error" v-if="item.status == 5 && item.auditStatus!=2"></u-tag>
					<u-tag text="审核未通过" type="error" v-if="item.status == 5 && item.auditStatus==2"></u-tag>
				</view>
				<view class="content">
					<view class="content_item" style="margin-top: 10rpx;">
						<view style="font-size: 26rpx;color: rgba(0, 0, 0, 0.5);">
							型号：
						</view>
						<view class="">
							{{item.modelName}}
						</view>
					</view>
					<view class="content_item" style="margin-top: 10rpx;">
						<view style="font-size: 26rpx;color: rgba(0, 0, 0, 0.5);">
							板材类型：
						</view>
						<view class="">
							{{item.type}}
						</view>
					</view>
					<view class="content_item" style="">
						<view style="font-size: 26rpx;color: rgba(0, 0, 0, 0.5);">
							数量：
						</view>
						<view class="">
							{{item.num}}/pcs
						</view>
					</view>
					<view class="content_item" style="">
						<view style="font-size: 26rpx;color: rgba(0, 0, 0, 0.5);">
							价格：
						</view>
						<view style="color: rgb(255,153,0);font-weight: bold;">
							￥{{item.totalPrice}}
						</view>
					</view>
					<view class="content_item" style="" v-if="item.auditContent">
						<view style="font-size: 26rpx;color: rgba(0, 0, 0, 0.5);">
							审核意见：
						</view>
						<view style="">
							{{item.auditContent}}
						</view>
					</view>
					<view class="content_item" style="" v-if="item.courierType == 1">
						<view style="font-size: 26rpx;color: rgba(0, 0, 0, 0.5);">
							快递单号：
						</view>
						<view style="">
							{{item.courierNo}}
						</view>
					</view>
					<view class="content_item" style="margin-top: 10rpx;" v-if="item.courierType == 2">
						<view style="font-size: 26rpx;color: rgba(0, 0, 0, 0.5);">
							物流：
						</view>
						<view class="">
							厂商独立运输
						</view>
					</view>
					<view class="content_item" style="">
						<view style="font-size: 26rpx;color: rgba(0, 0, 0, 0.5);">
							下单时间：
						</view>
						<view style="font-size: 24rpx;color: darkgray;">
							{{item.orderTime.substring(0,16)}}
						</view>
					</view>
				</view>
				<view class="btn">
					<view style="display: flex;gap: 30rpx;margin-right: 0rpx;flex-direction: row-reverse;margin-right: 10rpx;">
						<view @click="toOrderDetail(item.id)" style="background-color: rgb(59,156,255);color: #fff;text-align: center;width: 90rpx;height: 40rpx;line-height: 40rpx;border-radius: 5rpx;font-size: 20rpx;">
							查看
						</view>
						<view v-if="item.status == 1" @click.stop="payOrder(item)" style="background-color: rgb(249,174,61);color: #fff;text-align: center;width: 90rpx;height: 40rpx;line-height: 40rpx;border-radius: 5rpx;font-size: 20rpx;">
							付款
						</view>
						<view v-if="item.status == 3" @click.stop="shouhuo(item)" style="background-color: rgb(249,174,61);color: #fff;text-align: center;width: 90rpx;height: 40rpx;line-height: 40rpx;border-radius: 5rpx;font-size: 20rpx;">
							确认收货
						</view>
						<view v-if="item.status == 4" @click.stop="back(item)" style="background-color: rgb(249,174,61);color: #fff;text-align: center;width: 90rpx;height: 40rpx;line-height: 40rpx;border-radius: 5rpx;font-size: 20rpx;">
							返单
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {listOrder,updateOrder,payOrder} from "@/api/pcb/order.js"
	export default {
		data() {
			return {
				userId:'',
				current:0,
				tabsList:[
					{
						name:'全部',
						status:null
					},
					{
						name:'待付款',
						status:1
					},
					{
						name:'待发货',
						status:2
					},
					{
						name:'待收货',
						status:3
					},
					{
						name:'已完成',
						status:4
					},
					{
						name:'已取消',
						status:5
					},
				],
				orderList:[],
				params:{},
				queryParams:{
					userId:this.$store.state.user.userId,
					isOrder:1,
					status:null,
					pageSize:1000000,
					modelName:''
				}
			}
		},
		onLoad({status}) {
			let current = parseInt(status)+1
			this.current = current
			this.userId = this.$store.state.user.userId
			this.queryParams.status = this.tabsList[current].status
			this.getOrderList()
		},
		methods: {
			getOrderList(){	
				listOrder(this.queryParams).then(res=>{
					this.orderList = res.rows
				})
			},
			searchInput(e){
				this.queryParams.modelName = e
				this.getOrderList()
			},
			tabsClick(item){
				
			},
			tabsChange(e){
				this.current = e.index
				this.queryParams.status = e.status
				this.getOrderList()
			},
			payOrder(item){
				console.log(item);
				uni.showLoading({
					title:"加载中……",
					mask:true
				})
				if(item.prepayId == null){
					item.status = 2
					wx.login({
						success:loginRes=>{
							item.code = loginRes.code
							payOrder(item.id).then(payRes=>{
								uni.hideLoading()
								wx.requestPayment({
									timeStamp:payRes.data.timeStamp,
									package:'prepay_id='+payRes.data.package,
									nonceStr:payRes.data.nonceStr,
									paySign:payRes.data.paySign,
									signType:'RSA',
									fail: (err) => {
									},
									success: (success) => {
										uni.showModal({
											title:"成功",
											content:"支付成功！",
											showCancel:false,
										})
									}
								})
							})
						}
					})
				}else{
					payOrder(item.id).then(payRes=>{
						uni.hideLoading()
						wx.requestPayment({
							timeStamp:payRes.data.timeStamp,
							package:'prepay_id='+payRes.data.package,
							nonceStr:payRes.data.nonceStr,
							paySign:payRes.data.paySign,
							signType:'RSA',
							fail: (err) => {
							},
							success: (success) => {
								uni.showModal({
									title:"成功",
									content:"支付成功！",
									showCancel:false,
								})
							}
						})
					})
				}
			},
			shouhuo(item){
				item.status = 4
				updateOrder(item)
			},
			toOrderDetail(id){
				uni.navigateTo({
					url:"/pages/mine/OrderDetail/OrderDetail?id="+id
				})
			},
			back(item){
				let i = JSON.parse(JSON.stringify(item));
				uni.showModal({
					title: '返单',
					content: '',
					editable:true,
					placeholderText:"请输入数量",
					success: res => {
						if(res.confirm){
							this.params = i
							this.params.num = res.content
							this.params.isBack = 1
							this.params.id = null
							this.params.status = 1
							this.params.courierNo = null
							this.params.courierType = null
							this.params.auditContent = null
							console.log(this.params);
							uni.showLoading({
								title:"正在生成订单……",
								mask:true
							})
							setTimeout(()=>{
								uni.navigateTo({
									url:"/pages/jiesuan/jiesuan",
									success: () => {
										uni.hideLoading()
									}
								})
							},5000)
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.page{
		.topBack{
			background-color: rgb(147,116,84);
			width: 100vw;
			height: 200rpx;
			position: absolute;
			z-index: -999;
			border-radius: 100% / 0 0 50% 50%;
		}
		.tab{
			width: 90vw;
			margin: 10rpx auto;
		}
		.empty{
			width: 95vw;
			margin: 20rpx auto;
			box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
			height: 400rpx;
			background-color: #fff;
			border-radius: 12rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			image{
				width: 300rpx;
				height: 300rpx;
			}
			text{
				color: rgba(0, 0, 0, 0.4);
			}
		}
		.orderList{
			.orderItem{
				width: 95vw;
				margin: 20rpx auto;
				background-color: #fff;
				box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
				padding: 20rpx;
				border-radius: 12rpx;
				.title{
					display: flex;
					justify-content: space-between;
					align-items: center;
					border-bottom: 1rpx solid rgba(0, 0, 0, 0.4);
					padding-bottom: 10rpx;
					font-weight: bold;
				}
				.content{
					display: flex;
					flex-direction: column;
					gap: 6rpx;
					.content_item{
						display: flex;
						justify-content: space-between;
					}
				}
				.btn{
					display: flex;
					justify-content: flex-end;
				}
			}
		}
	}
</style>
