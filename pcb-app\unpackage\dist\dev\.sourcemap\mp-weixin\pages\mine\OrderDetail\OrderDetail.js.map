{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/OrderDetail/OrderDetail.vue?f038", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/OrderDetail/OrderDetail.vue?8f5e", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/OrderDetail/OrderDetail.vue?e5c2", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/OrderDetail/OrderDetail.vue?39fe", "uni-app:///pages/mine/OrderDetail/OrderDetail.vue", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/OrderDetail/OrderDetail.vue?9163", "webpack:///D:/项目/PCB小程序/wk_pcb/pcb-app/pages/mine/OrderDetail/OrderDetail.vue?7585"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "detail", "baseUrl", "onLoad", "methods", "getDetail", "console", "<PERSON>n<PERSON>", "xia<PERSON>", "uni", "title", "mask", "url", "success", "showMenu", "filePath", "fileType", "fail", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AAC4K;AAC5K,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAqpB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmLzqB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACAC;MACAC;QACAC;QACAC;MACA;MACAF;QACA;QACAG;QACAC;UACAJ;YACAK;YACAC;YACAC;YACAH;cACAJ;cACAH;YACA;YACAW;cACAX;YACA;UACA;QACA;QACAW;UACAR;YACAS;YACAP;YACAD;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5OA;AAAA;AAAA;AAAA;AAAouC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAxvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/OrderDetail/OrderDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/OrderDetail/OrderDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./OrderDetail.vue?vue&type=template&id=17f8bfec&\"\nvar renderjs\nimport script from \"./OrderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./OrderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./OrderDetail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/OrderDetail/OrderDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderDetail.vue?vue&type=template&id=17f8bfec&\"", "var components\ntry {\n  components = {\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tag/u-tag\" */ \"@/uni_modules/uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uCollapse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-collapse/u-collapse\" */ \"@/uni_modules/uview-ui/components/u-collapse/u-collapse.vue\"\n      )\n    },\n    uCollapseItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-collapse-item/u-collapse-item\" */ \"@/uni_modules/uview-ui/components/u-collapse-item/u-collapse-item.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderDetail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"order_detail_page\">\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"order_no_title\">\r\n\t\t\t\t<view class=\"order_no\">\r\n\t\t\t\t\t{{detail.orderNo}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"status\">\r\n\t\t\t\t\t<u-tag v-if=\"detail.status == 0\" text=\"审核中\" type=\"info\" />\r\n\t\t\t\t\t<u-tag v-if=\"detail.status == 1\" text=\"待支付\" type=\"info\" />\r\n\t\t\t\t\t<u-tag v-if=\"detail.status == 2\" text=\"待发货\" type=\"primary\" />\r\n\t\t\t\t\t<u-tag v-if=\"detail.status == 4\" text=\"已完成\" type=\"success\" />\r\n\t\t\t\t\t<u-tag v-if=\"detail.status == 5\" text=\"已取消\" type=\"error\" />\r\n\t\t\t\t\t<u-tag v-if=\"detail.status == 3\" text=\"待收货\" type=\"warning\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"container_item\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t材料类型：\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t{{detail.type}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"container_item\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t板子大小：\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t{{detail.pcbWidth}}*{{detail.pcbHeight}}mm\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"container_item\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t板子数量：\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t{{detail.num}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"container_item\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t拼版款数：\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t{{detail.styleCount1}}X{{detail.styleCount2}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"container_item\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t总面积：\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t{{detail.area}}㎡\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"container_item\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t开票方式：\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t{{detail.billType}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"container_item\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t总价：\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t￥{{detail.totalPrice}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"container_item\" v-if=\"detail.status == 4\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t申请钢网资料：\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t<u-button v-if=\"detail.applicationFile == 0\" type=\"primary\" text=\"申请\" @click=\"shenqing\"></u-button>\r\n\t\t\t\t\t<u-tag v-if=\"detail.applicationFile == 1\" text=\"已申请\" type=\"warning\" />\r\n\t\t\t\t\t<u-button v-if=\"detail.applicationFile == 2\" type=\"primary\" text=\"下载\" @click=\"xiazai\"></u-button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t  <u-collapse>\r\n\t\t\t    <u-collapse-item  title=\"详细参数\" name=\"Docs guide\">\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t厚度：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.thickness}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t铜箔厚度：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.cuThickness}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t层数：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.layer}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t阻焊颜色：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.weldColor}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t字符颜色：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.charColor}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t阻焊覆盖：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.cover}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t焊盘喷锡：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.surface}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\" v-if=\"detail.thermalConductivity !== '不显示'\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t导热系数：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.thermalConductivity}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t测试方式：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.test}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t成型：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.molding}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"collapse_item\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t出货方式：\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t\t\t{{detail.outWay}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t    </u-collapse-item>\r\n\t\t\t  </u-collapse>\r\n\t\t</view>\n\t</view>\n</template>\n\n<script>\r\n\timport { baseUrl } from \"../../../config\"\r\n\timport { getOrder, updateOrder } from \"@/api/pcb/order.js\"\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tdetail:{},\r\n\t\t\t\tbaseUrl,\n\t\t\t}\n\t\t},\r\n\t\tonLoad({id}) {\r\n\t\t\tthis.getDetail(id)\r\n\t\t},\n\t\tmethods: {\n\t\t\tgetDetail(id){\r\n\t\t\t\tgetOrder(id).then(res=>{\r\n\t\t\t\t\tthis.detail = res.data\r\n\t\t\t\t\tconsole.log(this.detail);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tshenqing(){\r\n\t\t\t\tthis.detail.applicationFile = 1\r\n\t\t\t\tupdateOrder(this.detail).then(res=>{\r\n\t\t\t\t\tthis.getDetail(this.detail.id)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\txiazai(){\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle:\"下载中……\",\r\n\t\t\t\t\tmask:true\r\n\t\t\t\t})\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t// url:this.baseUrl+'/common/download?fileName='+this.detail.informationFile,\r\n\t\t\t\t\turl:this.baseUrl+this.detail.informationFile,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\tshowMenu:true,\r\n\t\t\t\t\t\t\tfilePath:res.tempFilePath,\r\n\t\t\t\t\t\t\tfileType:['pdf'],\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t\tconsole.log(\"打开成功\");\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t\ttitle: '失败请重新下载',\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\r\n\t.order_detail_page{\r\n\t\tpadding-top: 30rpx;\r\n\t\t.container{\r\n\t\t\twidth: 90vw;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n\t\t\tpadding: 20rpx;\r\n\t\t\t.order_no_title{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t.order_no{\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.container_item{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 20rpx 30rpx;\r\n\t\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.1);\r\n\t\t\t\t.title{\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t\t.desc{\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.collapse_item{\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tpadding-bottom: 20rpx;\r\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.1);\r\n\t\t.title{\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t\t.desc{\r\n\t\t\t\r\n\t\t}\r\n\t}\n</style>\n", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderDetail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderDetail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753541881774\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}