<template>
  <view class="mine-container" :style="{height: `${windowHeight}px`}">
    <!--顶部个人信息栏-->
	<div class="topBack"></div>
    <view class="header-section">
      <view class="flex padding justify-between">
        <view class="flex align-center">
          <view v-if="!avatar" class="cu-avatar xl round bg-white">
            <view class="iconfont icon-people text-gray icon"></view>
          </view>
          <image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round" mode="widthFix">
          </image>
          <view v-if="!name" @click="handleToLogin" class="login-tip">
            点击登录
          </view>
          <view v-if="name" @click="handleToInfo" class="user-info">
            <view class="u_title">
              用户名：{{ name }}
            </view>
          </view>
        </view>
        <view @click="handleToInfo" class="flex align-center">
          <text>个人信息</text>
          <view class="iconfont icon-right"></view>
        </view>
      </view>
    </view>
	
	<view class="orderContainer">
		<view class="orderTitle" @click="toMyOrder(-1)">
			<text class="title">我的订单</text>
			<view class="allOrder">
				全部订单
				<u-icon name="arrow-right" color="rgba(0, 0, 0, 0.5)"></u-icon>
			</view>
		</view>
		<view class="orderList">
			<view class="orderItem" @click="toMyOrder(0)">
				<image src="../../static/images/mine/daifukuan.png" />
				<text>待付款</text>
			</view>
			<view class="orderItem" @click="toMyOrder(1)">
				<image src="../../static/images/mine/daifahuo.png" />
				<text>待发货</text>
			</view>
			<view class="orderItem" @click="toMyOrder(2)">
				<image src="../../static/images/mine/daishouhuo.png" />
				<text>待收货</text>
			</view>
			<view class="orderItem" @click="toMyOrder(3)">
				<image src="../../static/images/mine/yiwancheng.png" />
				<text>已完成</text>
			</view>
			<view class="orderItem" @click="toMyOrder(4)">
				<image src="../../static/images/mine/yiquxiao.png" />
				<text>已取消</text>
			</view>
		</view>
	</view>

    <view class="content-section">
      <view class="menu-list">
        <view class="list-cell list-cell-arrow" @click="handleToEditInfo">
          <view class="menu-item-box">
            <!-- <view class="iconfont icon-user menu-icon"></view> -->
            <view>编辑资料</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="toMyAddress">
          <view class="menu-item-box">
            <view>地址管理</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleLogout">
          <view class="menu-item-box">
            <!-- <view class="iconfont icon-setting menu-icon"></view> -->
            <view>退出登录</view>
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
  import storage from '@/utils/storage'
  
  export default {
    data() {
      return {
        name: this.$store.state.user.name,
        version: getApp().globalData.config.appInfo.version
      }
    },
    computed: {
      avatar() {
        return this.$store.state.user.avatar
      },
      windowHeight() {
        return uni.getSystemInfoSync().windowHeight - 50
      }
    },
    methods: {
      handleToInfo() {
        this.$tab.navigateTo('/pages/mine/info/index')
      },
      handleToEditInfo() {
        this.$tab.navigateTo('/pages/mine/info/edit')
      },
      handleToSetting() {
        this.$tab.navigateTo('/pages/mine/setting/index')
      },
      handleToLogin() {
        this.$tab.reLaunch('/pages/login')
      },
      handleToAvatar() {
        this.$tab.navigateTo('/pages/mine/avatar/index')
      },
      handleLogout() {
        this.$modal.confirm('确定注销并退出系统吗？').then(() => {
          this.$store.dispatch('LogOut').then(() => {
            this.$tab.reLaunch('/pages/index')
          })
        })
      },
      handleHelp() {
        this.$tab.navigateTo('/pages/mine/help/index')
      },
      handleAbout() {
        this.$tab.navigateTo('/pages/mine/about/index')
      },
      handleBuilding() {
        this.$modal.showToast('模块建设中~')
      },
	  toMyOrder(status){
		  this.$tab.navigateTo('/pages/mine/myOrder/myOrder?status='+status)
	  },
	  toMyAddress(){
		  uni.navigateTo({
		  	url:"/pages/mine/myAddress/myAddress"
		  })
	  }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #f5f6f7;
  }
  .mine-container {
	padding: 0.1rpx;
    width: 100%;
    height: 100%;
	.topBack{
		background-color: rgb(147,116,84);
		width: 100vw;
		height: 200rpx;
		position: absolute;
		z-index: -999;
		border-radius: 100% / 0 0 50% 50%;
	}
	.orderContainer{
		background-color: #fff;
		width: 95vw;
		margin: 20rpx auto;
		// height: 200rpx;
		padding: 40rpx;
		border-radius: 12rpx;
		box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
		.orderTitle{
			display: flex;
			justify-content: space-between;
			.title{
				font-weight: bold;
			}
			.allOrder{
				display: flex;
				justify-content: center;
				gap: 10rpx;
				color: rgba(0, 0, 0, 0.5);
			}
		}
		.orderList{
			display: flex;
			margin-top: 30rpx;
			gap: 65rpx;
			.orderItem{
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				gap: 10rpx;
				image{
					width: 50rpx;
					height: 50rpx;
				}
				text{
					font-size: 24rpx;
				}
			}
		}
	}
    .header-section {
      // padding: 15px 15px 45px 15px;
      background-color: #fff;
	  width: 95vw;
	  margin: 20rpx auto;
	  box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
	  border-radius: 12rpx;

      .login-tip {
        font-size: 18px;
        margin-left: 10px;
      }

      .cu-avatar {
        border: 2px solid #eaeaea;

        .icon {
          font-size: 40px;
        }
      }

      .user-info {
        margin-left: 15px;

        .u_title {
          font-size: 18px;
          line-height: 30px;
        }
      }
    }

    .content-section {
      position: relative;
      top: 0px;

      .mine-actions {
        margin: 15px 15px;
        padding: 20px 0px;
        border-radius: 8px;
        background-color: white;

        .action-item {
          .icon {
            font-size: 28px;
          }

          .text {
            display: block;
            font-size: 13px;
            margin: 8px 0px;
          }
        }
      }
    }
  }
</style>
