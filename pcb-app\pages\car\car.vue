<template>
	<view class="page">
		<view class="topBack" />
		<view class="empty" v-if="userId==null||orderList.length<=0">
			<image src="../../static/images/cart/emptyCar.png" />
			<text v-if="userId == null">
				请点击
				<text @click="toLogin" style="color: rgb(255,153,0);margin: 0 10rpx;">立即登录</text>
				查看
			</text>
			<text v-if="orderList.length<=0&&userId">
				您的购物车是空的，请点击
				<text @click="toWork" style="color: rgb(255,153,0);margin: 0 10rpx;">在线计价</text>
				下单
			</text>
		</view>
		<view class="orderList">
			<view class="orderItem" v-for="item in orderList" :key="item.id">
				<view class="title">
					<view @click="toOrderDetail(item.id)">
						<u-checkbox  :checked="chooseItem.includes(item)" @change="checkBoxChange(item)" activeColor="rgb(255,153,0)" />
						<view style="font-size: 32rpx;font-weight: bold;">
							{{item.orderNo}}
						</view>
						<view class="">
							<uni-icons type="right" size="32rpx"></uni-icons>
						</view>
					</view>
					<view style="color: darkgray;">
						{{item.createTime.substring(0,16)}}
					</view>
				</view>
				<view class="content">
					<view class="detail">
						<view class="detail_title">
							基本信息:
						</view>
						<view class="detail_desc">
							{{item.pcbWidth}}mm*{{item.pcbHeight}}mm {{item.layer}}
						</view>
					</view>
					<view class="detail">
						<view class="detail_title">
							数量:
						</view>
						<view class="detail_desc">
							{{item.num}}PCS
						</view>
					</view>
					<view class="detail">
						<view class="detail_title">
							型号:
						</view>
						<view class="detail_desc">
							{{item.modelName}}
						</view>
					</view>
					<view class="detail">
						<view class="detail_title">
							价格:
						</view>
						<view class="detail_desc">
							￥{{item.totalPrice}}
						</view>
					</view>
					<view class="detail">
						<view class="detail_title">
							上传文件:
						</view>
						<view class="detail_desc">
							<view class="up_btn" @click="choose(item)">
								<text v-if="item.file == null || item.file == ''">上传文件</text>
								<text v-else>重新选择</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="bottom_container" v-if="orderList.length>0">
			<view class="left">
				<u-checkbox  label="全选" :checked="orderList.length == chooseItem.length" @change="allCheckBoxChange" activeColor="rgb(255,153,0)" />
				<view style="color: rgba(0,0,0,0.3);" @click="deleteItem">删除</view>
			</view>
			<view class="right">
				<view style="width: 160rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;">
					<text>总计：</text>
					<text style="color: rgb(255,153,0);font-weight: bold;">￥{{totalPrice}}</text>
				</view>
				<view style="height: 80rpx;">
					<u-button @click="next" type="warning" :text="chooseItem.length>0?'下一步('+chooseItem.length+')':'下一步'"></u-button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { listOrder,updateOrder,pay,delOrder } from "@/api/pcb/order.js"
	import upload from '@/utils/upload'
	import {toast} from "@/utils/common.js"
	export default {
		data() {
			return {
				orderList:[],
				userId:null,
				chooseItem:[],
				totalPrice:0
			}
		},
		onShow() {
			this.userId = this.$store.state.user.userId
			this.getOrderList()
			this.chooseItem = []
		},
		methods: {
			getOrderList(){
				listOrder({pageSize:100000,isOrder:0,userId:this.userId}).then(res=>{
					this.chooseItem = []
					this.orderList = res.rows
				})
			},
			checkBoxChange(item){
				if(this.chooseItem.includes(item)){
					this.chooseItem.splice(this.chooseItem.indexOf(item),1)
				}else{
					this.chooseItem.push(item)
				}
			},
			next(){
				if(this.chooseItem.length<=0){
					toast("请选择订单")
					return
				}
				for(let i=0;i<this.chooseItem.length;i++){
					if(this.chooseItem[i].file=='' || this.chooseItem[i].file == null){
						toast('请上传文件')
						return
					}
				}
				uni.navigateTo({
					url:"/pages/payPage/payPage"
				})
				// pay(this.chooseItem).then(res=>{
				// 	this.getOrderList()
				// 	uni.showToast({
				// 		title:"可前往我的订单查看",
				// 		icon:"success"
				// 	})
				// })
			},
			toLogin(){
				uni.navigateTo({
					url:"/pages/login"
				})
			},
			toWork(){
				uni.switchTab({
					url:"/pages/work/index"
				})
			},
			deleteItem(){
				if(this.chooseItem.length<=0){
					toast('请选择要删除的订单！')
					return
				}
				uni.showModal({
					title:"提示",
					content:'确定删除已选中的订单吗？',
					success: (res) => {
						if(res.confirm){
							let ids = this.chooseItem.map(item=>item.id)
							delOrder(ids).then(res=>{
								this.getOrderList()
								this.chooseItem = this.chooseItem.filter(item=>!ids.includes(item.id))
							})
						}
					}
				})
			},
			choose(item){
				wx.chooseMessageFile({
					count:1,
					success:(res)=>{
						console.log(res);
						if(res.tempFiles[0].size > 52428800){
							uni.showToast({
								title:"上传大小不能超过50mb",
								icon:"none"
							})
							return
						}
						uni.showLoading({
							title:"上传中……"
						})
						let file = res.tempFiles[0]
						upload({
							url:'/common/upload',
							name:'file',
							filePath:file.path
						}).then(uploadRes=>{
							item.file = uploadRes.fileName
							updateOrder(item).then(()=>{
								uni.hideLoading()
								this.getOrderList()
							})
						}).catch(err=>{
							uni.showToast({
								title:"上传失败",
								icon:"error"
							})
							console.log(err);
						})
					}
				})
			},
			allCheckBoxChange(){
				if(this.chooseItem.length>0){
					this.chooseItem = []
				}else{
					this.orderList.forEach(item=>{
						this.chooseItem.push(item)
					})
				}
			},
			toOrderDetail(id){
				uni.navigateTo({
					url:"/pages/orderDetail/orderDetail?id="+id
				})
			}
		},
		watch:{
			chooseItem(oldVal,newVal){
				this.totalPrice = 0
				newVal.forEach(item=>{
					this.totalPrice+=item.totalPrice
				})
			}
		}
	}
</script>

<style lang="scss">
	.page{
		padding-top: 0.1rpx;
		.topBack{
			background-color: rgb(147,116,84);
			width: 100vw;
			height: 280rpx;
			position: absolute;
			z-index: -999;
			border-radius: 100% / 0 0 50% 50%;
		}
		.empty{
			width: 95vw;
			margin: 20rpx auto;
			box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
			height: 400rpx;
			background-color: #fff;
			border-radius: 12rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			image{
				width: 300rpx;
				height: 300rpx;
			}
		}
		.orderList{
			margin-top: 40rpx;
			.orderItem{
				width: 95vw;
				background-color: #fff;
				border-radius: 12rpx;
				margin: 20rpx auto;
				padding: 30rpx 50rpx;
				box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(0, 0, 0, 0.06);
				.title{
					display: flex;
					justify-content: space-between;
					padding-bottom: 20rpx;
					border-bottom: 1rpx solid rgba(0, 0, 0, 0.2);
					view{
						display: flex;
						justify-content: space-between;
						align-items: center;
						gap: 5rpx;
					}
				}
				.content{
					margin-top: 20rpx;
					display: flex;
					flex-direction: column;
					gap: 20rpx;
					.detail{
						display: flex;
						.detail_title{
							width: 150rpx;
							color: darkgray;
						}
						.detail_desc{
							font-weight: bold;
							.up_btn{
								font-weight: 100;
								font-size: 24rpx;
								background-color: rgb(245,245,245);
								padding: 10rpx;
							}
						}
					}
				}
			}
		}
		.bottom_container{
			position: fixed;
			bottom: 0rpx;
			background-color: #fff;
			width: 100vw;
			height: 140rpx;
			padding: 10rpx 40rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.left{
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 180rpx;
			}
			.right{
				display: flex;
				width: 280rpx;
				justify-content: space-between;
			}
		}
	}
</style>
