import request from '@/utils/request'

// 查询计价模板列表
export function listPriceModel(query) {
  return request({
    url: '/pcb/priceModel/list',
    method: 'get',
    params: query
  })
}

// 查询计价模板详细
export function getPriceModel(id) {
  return request({
    url: '/pcb/priceModel/' + id,
    method: 'get'
  })
}

// 新增计价模板
export function addPriceModel(data) {
  return request({
    url: '/pcb/priceModel',
    method: 'post',
    data: data
  })
}

// 修改计价模板
export function updatePriceModel(data) {
  return request({
    url: '/pcb/priceModel',
    method: 'put',
    data: data
  })
}

// 删除计价模板
export function delPriceModel(id) {
  return request({
    url: '/pcb/priceModel/' + id,
    method: 'delete'
  })
}
