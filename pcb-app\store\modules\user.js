import config from '@/config'
import storage from '@/utils/storage'
import constant from '@/utils/constant'
import { login, logout, getInfo,wxLogin } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'

const baseUrl = config.baseUrl

const user = {
  state: {
    token: getToken(),
    name: storage.get(constant.name),
    avatar: storage.get(constant.avatar),
    roles: storage.get(constant.roles),
    permissions: storage.get(constant.permissions),
	userId: storage.get(constant.userId)
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
      storage.set(constant.name, name)
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
      storage.set(constant.avatar, avatar)
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
      storage.set(constant.roles, roles)
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
      storage.set(constant.permissions, permissions)
    },
	SET_USER_ID: (state,userId)=>{
		state.userId = userId
		storage.set(constant.userId,userId)
	}
  },

  actions: {
	  Login({ commit }, userInfo) {
	    const username = userInfo.username.trim()
	    const password = userInfo.password
	    const code = userInfo.code
	    const uuid = userInfo.uuid
	    return new Promise((resolve, reject) => {
	      login(username, password, code, uuid).then(res => {
	        setToken(res.token)
	        commit('SET_TOKEN', res.token)
	        resolve()
	      }).catch(error => {
	        reject(error)
	      })
	    })
	  },
	//微信登录
	WxLogin({commit},userInfo) {
		const nickName = userInfo.nickName
		const avatarUrl = userInfo.avatarUrl
		const code = userInfo.code
		const aaa = userInfo.ocode
		console.log(userInfo);
		return new Promise((resolve,reject)=>{
			wxLogin(code,avatarUrl,nickName,aaa).then(res=>{
				setToken(res.token)
				commit('SET_TOKEN',res.token)
				resolve()
			}).catch(error=>{
				reject(error)
			})
		})
	},

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user
		  let avatar = ''
		  if(user == null || user.avatar == "" || user.avatar == null){
			  avatar = require("@/static/images/profile.jpg")
		  }else{
			  if(user.avatar.indexOf("http")>=0){
				  avatar = user.avatar
			  }else{
				  avatar = baseUrl + user.avatar
			  }
		  }
		  const userId = user.userId
          const nickname = (user == null || user.nickName == "" || user.nickName == null) ? "" : user.nickName
          if (res.roles && res.roles.length > 0) {
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
		  commit('SET_USER_ID',userId)
          commit('SET_NAME', nickname)
          commit('SET_AVATAR', avatar)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
		  commit('SET_NAME','')
          removeToken()
          storage.clean()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}

export default user
