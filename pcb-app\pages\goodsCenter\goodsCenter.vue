<template>
	<page-meta :page-style="'overflow:'+(detailShow?'hidden':'visible')"></page-meta>
	<view class="page">
		<view class="topBack" />
		<u-tabs class="tab" :list="categoryList" @click="tabsClick" :activeStyle="{'color':'#fff','font-size':'35rpx'}" :inactiveStyle="{'color':'#fff','font-size':'35rpx'}" lineColor="rgb(255,153,0)"
		        lineWidth="90rpx" :itemStyle="{'height':'85rpx','min-width':'160rpx'}" />
		<view class="goodsList">
			<view @click="showDetail(item.id)" class="goodsItem" v-for="item in goodsList" :key="item.id">
				<image :src="baseUrl + item.pic"/>
				<text class="title">{{item.name}}</text>
				<view class="tag">
					<span v-for="tagItem in item.tag.split('，')" :key="tagItem">{{tagItem}}</span>
				</view>
			</view>
		</view>
		<uni-popup ref="popup" background-color="#fff" class="popup" borderRadius="10px 10px 0 0">
			<view class="container" v-if="detail">
				<image :src="baseUrl + detail.pic" />
				<text class="title">{{detail.name}}</text>
				<view class="tag">
					<span v-for="tagItem in detail.tag.split('，')" :key="tagItem">{{tagItem}}</span>
				</view>
				<view class="desc" style="margin-top: 30rpx;">
					<text>层数/板厚：{{detail.layer}}</text>
					<text>表面处理：{{detail.surface}}</text>
				</view>
				<view class="desc">
					<text>线宽线距：{{detail.width}}</text>
					<text>最小孔距：{{detail.bores}}</text>
				</view>
				<view class="desc">
					<text>技术特点：{{detail.tech}}</text>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { baseUrl } from "../../config"
	import {listCategory} from "@/api/pcb/category.js"
	import {listGoods,getGoods} from "@/api/pcb/goods.js"
	export default {
		data() {
			return {
				categoryList:[],
				goodsList:[],
				baseUrl,
				detail:null,
				detailShow:false
			}
		},
		onLoad() {
			this.getCategoryList()
		},
		methods: {
			getCategoryList(){
				listCategory({pageSize:10000}).then(res=>{
					this.categoryList = res.rows
					this.categoryList.unshift({'name':'全部'})
					this.getGoodsList(null)
				})
				// console.log(this.categoryList);
			},
			getGoodsList(categoryId){
				listGoods({pageSize:10000,categoryId}).then(res=>{
					this.goodsList = res.rows
					console.log(res);
				})
			},
			tabsClick(e){
				this.getGoodsList(e.id)
			},
			showDetail(id){
				getGoods(id).then(res=>{
					this.detail = res.data
					this.$refs.popup.open()
					this.detailShow = true
					console.log(res);
				})
			},
			popupClose(){
				this.detailShow = false
			}
		}
	}
</script>

<style lang="scss">
	.page{
		min-height: 100vh;
		padding-top: 1rpx;
		.topBack{
			background-color: rgb(147,116,84);
			width: 100vw;
			height: 280rpx;
			position: absolute;
			z-index: -999;
			border-radius: 100% / 0 0 50% 50%;
		}
		.tab{
			margin: 50rpx auto;
			width: 90vw;
		}
		.goodsList{
			width: 95vw;
			margin: 10rpx auto;
			display: flex;
			justify-content: space-between;
			.goodsItem{
				background-color: #fff;
				width: 46vw;	
				border-radius: 12rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				padding: 20rpx;
				image{
					width: 95%;
					height: 300rpx;
				}
				.title{
					font-weight: bold;
					font-size: 32rpx;
				}
				.tag{
					margin-top: 10rpx;
					span{
						border: solid 1rpx rgb(255,153,0);
						color: rgb(255,153,0);
						background-color: rgba(255,153,0,0.1);
						margin-right: 20rpx;
						font-size: 20rpx;
						padding: 3rpx;
						border-radius: 6rpx;
					}
				}
			}
		}
		.popup{
			border-radius: 20rpx;
			.container{
				width: 100vw;
				border-radius: 16rpx;
				height: 800rpx;
				margin: 0 auto;
				display: flex;
				align-items: center;
				flex-direction: column;
				border-radius: 20rpx;
				image{
					width:100%;
					height: 400rpx;
				}
				.title{
					margin-top: 50rpx;
					font-weight: bold;
					font-size: 32rpx;
				}
				.tag{
					margin-top: 30rpx;
					span{
						border: solid 1rpx rgb(255,153,0);
						color: rgb(255,153,0);
						background-color: rgba(255,153,0,0.1);
						margin-right: 20rpx;
						font-size: 32rpx;
						padding: 10rpx;
						border-radius: 6rpx;
					}
				}
				.desc{
					display: flex;
					justify-content: space-between;
					width: 70vw;
					font-size: 24rpx;
				}
			}
		}
	}
</style>
